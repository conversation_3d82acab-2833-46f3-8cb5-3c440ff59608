import React, { useState, useMemo, useCallback } from 'react';
import { View, Text, Pressable, ScrollView } from 'react-native';
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
} from '@/components/ui/actionsheet';
import { ChevronDownIcon } from '@/components/ui/icon';
import { Icon } from '@/components/ui/icon';
import { triggerHapticFeedback } from '@/utils';

export interface DetailedOption {
  label: string;
  value: string;
  description?: string;
  icon?: React.ComponentType<any>;
  disabled?: boolean;
}

interface DetailedSelectProps {
  options: DetailedOption[];
  value?: string;
  placeholder?: string;
  onValueChange: (value: string) => void;
  disabled?: boolean;
  className?: string;
}

const TRIGGER_STYLES = {
  base: 'flex-row items-center justify-between w-full px-4 py-3 rounded-md border border-outline-300',
  enabled: 'bg-background-0',
  disabled: 'opacity-50 bg-background-50',
} as const;

const TEXT_STYLES = {
  trigger: {
    base: 'font-urbanistMedium text-base',
    selected: 'text-typography-900',
    placeholder: 'text-typography-500',
  },
  option: {
    label: {
      base: 'font-urbanistSemiBold text-base',
      selected: 'text-primary-700',
      unselected: 'text-typography-900',
      disabled: 'text-typography-400',
    },
    description: {
      base: 'font-urbanistMedium text-sm leading-5 mt-1',
      selected: 'text-primary-600',
      unselected: 'text-typography-600',
      disabled: 'text-typography-400',
    },
  },
} as const;

const OPTION_STYLES = {
  container: {
    base: 'rounded-lg p-4 border',
    selected: 'border-primary-0 border-2 bg-primary-0/10',
    unselected: 'border-outline-300 bg-background-0',
    disabled: 'border-outline-200 bg-background-50 opacity-70',
  },
  icon: {
    base: 'mt-1 mr-3 w-6 h-6',
    selected: 'text-primary-600',
    unselected: 'text-typography-700',
    disabled: 'text-typography-400',
  },
} as const;

const ICON_STYLES = {
  trigger: 'text-typography-700 mr-2',
  chevron: {
    base: 'text-typography-500',
    disabled: 'opacity-50',
  },
} as const;

const DetailedSelect: React.FC<DetailedSelectProps> = ({
  options,
  value,
  placeholder = 'Select',
  onValueChange,
  disabled = false,
  className = '',
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const selectedOption = useMemo(
    () => options.find((opt) => opt.value === value),
    [options, value]
  );

  const triggerClassName = useMemo(() => {
    const classes = [
      TRIGGER_STYLES.base,
      disabled ? TRIGGER_STYLES.disabled : TRIGGER_STYLES.enabled,
      className,
    ].filter(Boolean);
    return classes.join(' ');
  }, [disabled, className]);

  const triggerTextClassName = useMemo(() => {
    const classes = [
      TEXT_STYLES.trigger.base,
      selectedOption
        ? TEXT_STYLES.trigger.selected
        : TEXT_STYLES.trigger.placeholder,
    ];
    return classes.join(' ');
  }, [selectedOption]);

  const chevronClassName = useMemo(() => {
    const classes = [
      ICON_STYLES.chevron.base,
      disabled && ICON_STYLES.chevron.disabled,
    ].filter(Boolean);
    return classes.join(' ');
  }, [disabled]);

  const handleValueChange = useCallback(
    (selectedValue: string) => {
      if (selectedValue === value) {
        onValueChange('');
      } else {
        onValueChange(selectedValue);
      }
      setIsOpen(false);
    },
    [value, onValueChange]
  );

  const handleTriggerPress = useCallback(() => {
    triggerHapticFeedback();
    if (!disabled) {
      setIsOpen(true);
    }
  }, [disabled]);

  const handleClose = useCallback(() => {
    setIsOpen(false);
  }, []);

  const OptionItem = useCallback(
    ({ option, index }: { option: DetailedOption; index: number }) => {
      const isSelected = option.value === value;
      const isDisabled = option.disabled || false;

      const optionClassName = useMemo(() => {
        const classes = [
          OPTION_STYLES.container.base,
          index > 0 && 'mt-3',
          isDisabled
            ? OPTION_STYLES.container.disabled
            : isSelected
            ? OPTION_STYLES.container.selected
            : OPTION_STYLES.container.unselected,
        ].filter(Boolean);
        return classes.join(' ');
      }, [isSelected, isDisabled, index]);

      const iconClassName = useMemo(() => {
        const classes = [
          OPTION_STYLES.icon.base,
          isDisabled
            ? OPTION_STYLES.icon.disabled
            : isSelected
            ? OPTION_STYLES.icon.selected
            : OPTION_STYLES.icon.unselected,
        ];
        return classes.join(' ');
      }, [isSelected, isDisabled]);

      const labelClassName = useMemo(() => {
        const classes = [
          TEXT_STYLES.option.label.base,
          isDisabled
            ? TEXT_STYLES.option.label.disabled
            : isSelected
            ? TEXT_STYLES.option.label.selected
            : TEXT_STYLES.option.label.unselected,
        ];
        return classes.join(' ');
      }, [isSelected, isDisabled]);

      const descriptionClassName = useMemo(() => {
        const classes = [
          TEXT_STYLES.option.description.base,
          isDisabled
            ? TEXT_STYLES.option.description.disabled
            : isSelected
            ? TEXT_STYLES.option.description.selected
            : TEXT_STYLES.option.description.unselected,
        ];
        return classes.join(' ');
      }, [isSelected, isDisabled]);

      return (
        <Pressable
          key={option.value}
          onPress={() => {
            if (!isDisabled) {
              triggerHapticFeedback();
              handleValueChange(option.value);
            }
          }}
          disabled={isDisabled}
          className={optionClassName}
        >
          <View className="flex-row items-start">
            {option.icon && <Icon as={option.icon} className={iconClassName} />}
            <View className="flex-1">
              <Text className={labelClassName}>{option.label}</Text>
              {option.description && (
                <Text className={descriptionClassName}>
                  {option.description}
                </Text>
              )}
            </View>
          </View>
        </Pressable>
      );
    },
    [value, handleValueChange]
  );

  return (
    <>
      {/* Custom Trigger */}
      <Pressable
        onPress={handleTriggerPress}
        disabled={disabled}
        className={triggerClassName}
      >
        <View className="flex-row items-center flex-1">
          {selectedOption?.icon && (
            <Icon
              as={selectedOption.icon}
              size="xl"
              className={ICON_STYLES.trigger}
            />
          )}
          <Text className={triggerTextClassName}>
            {selectedOption?.label || placeholder}
          </Text>
        </View>
        <Icon as={ChevronDownIcon} size="xl" className={chevronClassName} />
      </Pressable>

      {/* Custom ActionSheet */}
      <Actionsheet isOpen={isOpen} onClose={handleClose}>
        <ActionsheetBackdrop />
        <ActionsheetContent className="max-h-[70vh]">
          <ActionsheetDragIndicatorWrapper>
            <ActionsheetDragIndicator />
          </ActionsheetDragIndicatorWrapper>

          <ScrollView
            className="w-full px-2 mt-4"
            showsVerticalScrollIndicator={false}
          >
            {options.map((option, index) => (
              <OptionItem key={option.value} option={option} index={index} />
            ))}
          </ScrollView>
        </ActionsheetContent>
      </Actionsheet>
    </>
  );
};

export default DetailedSelect;
