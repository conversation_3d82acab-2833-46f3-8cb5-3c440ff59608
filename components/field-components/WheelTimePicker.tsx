import React, { useState, useRef, useCallback, useMemo, memo } from 'react';
import {
  View,
  Text,
  ScrollView,
  Dimensions,
  NativeScrollEvent,
  NativeSyntheticEvent,
} from 'react-native';
import { triggerHapticFeedback } from '@/utils';

interface TimeValue {
  hour: number;
  minute: number;
  period: 'AM' | 'PM';
}

interface WheelTimePickerProps {
  value: TimeValue;
  onChange: (time: TimeValue) => void;
}

interface WheelPickerProps {
  data: readonly (string | number)[];
  selectedIndex: number;
  onValueChange: (index: number) => void;
  width: number;
}

const WHEEL_CONFIG = {
  ITEM_HEIGHT: 38,
  VISIBLE_ITEMS: 3,
  SCROLL_THRESHOLD: 0.3,
  UPDATE_THROTTLE: 50,
  WHEEL_PADDING: 80,
} as const;

const WHEEL_HEIGHT = WHEEL_CONFIG.ITEM_HEIGHT * WHEEL_CONFIG.VISIBLE_ITEMS;

const HOURS_DATA = Array.from({ length: 12 }, (_, i) => i + 1);
const MINUTES_DATA = Array.from({ length: 12 }, (_, i) =>
  (i * 5).toString().padStart(2, '0')
);
const PERIODS_DATA = ['AM', 'PM'] as const;

const WheelPicker = memo<WheelPickerProps>(
  ({ data, selectedIndex, onValueChange, width }) => {
    const scrollViewRef = useRef<ScrollView>(null);
    const [isScrolling, setIsScrolling] = useState(false);
    const [currentCenterIndex, setCurrentCenterIndex] = useState(() =>
      Math.max(0, Math.min(selectedIndex, data.length - 1))
    );

    const lastScrollY = useRef(0);
    const lastScrollTime = useRef(0);
    const lastHapticIndex = useRef(-1);
    const lastUpdateTime = useRef(0);

    const handleScroll = useCallback(
      (event: NativeSyntheticEvent<NativeScrollEvent>) => {
        const currentY = event.nativeEvent.contentOffset.y;
        const currentTime = Date.now();

        lastScrollY.current = currentY;
        lastScrollTime.current = currentTime;

        const exactPosition = currentY / WHEEL_CONFIG.ITEM_HEIGHT;
        const centerIndex = Math.round(exactPosition);
        const distanceFromCenter = Math.abs(exactPosition - centerIndex);
        const timeSinceLastUpdate = currentTime - lastUpdateTime.current;

        if (
          centerIndex >= 0 &&
          centerIndex < data.length &&
          distanceFromCenter < WHEEL_CONFIG.SCROLL_THRESHOLD &&
          centerIndex !== currentCenterIndex &&
          timeSinceLastUpdate > WHEEL_CONFIG.UPDATE_THROTTLE
        ) {
          setCurrentCenterIndex(centerIndex);
          onValueChange(centerIndex);
          lastUpdateTime.current = currentTime;

          if (lastHapticIndex.current !== centerIndex) {
            triggerHapticFeedback();
            lastHapticIndex.current = centerIndex;
          }
        }
      },
      [data.length, currentCenterIndex, onValueChange]
    );

    const handleScrollBeginDrag = useCallback(() => {
      setIsScrolling(true);
      lastScrollTime.current = Date.now();
      lastScrollY.current = 0;
    }, []);

    const handleScrollEndDrag = useCallback(() => {
      setIsScrolling(false);
    }, []);

    const handleMomentumScrollEnd = useCallback(() => {
      setIsScrolling(false);
    }, []);

    React.useEffect(() => {
      setCurrentCenterIndex(selectedIndex);
      if (!isScrolling) {
        const targetY = selectedIndex * WHEEL_CONFIG.ITEM_HEIGHT;
        scrollViewRef.current?.scrollTo({ y: targetY, animated: false });
      }
    }, [selectedIndex, isScrolling]);

    const wheelItemStyle = useMemo(
      () => ({
        paddingVertical: WHEEL_CONFIG.ITEM_HEIGHT,
      }),
      []
    );

    const highlightStyle = useMemo(
      () => ({
        top: WHEEL_CONFIG.ITEM_HEIGHT,
        height: WHEEL_CONFIG.ITEM_HEIGHT,
        zIndex: 10,
      }),
      []
    );

    const renderItem = useCallback(
      (item: string | number, index: number) => {
        const distance = Math.abs(index - currentCenterIndex);
        const isCenter = distance === 0;
        const isAdjacent = distance === 1;
        const opacity = isCenter ? 1.0 : isAdjacent ? 0.4 : 0.1;
        const scale = isCenter ? 1.0 : isAdjacent ? 0.85 : 0.7;

        return (
          <View
            key={index}
            style={{
              height: WHEEL_CONFIG.ITEM_HEIGHT,
              transform: [{ scale }],
              opacity,
            }}
            className="justify-center items-center"
          >
            <Text
              className={`text-lg ${
                isCenter
                  ? 'font-urbanistExtraBold text-base text-primary-0'
                  : isAdjacent
                  ? 'font-urbanistBold text-primary-300'
                  : 'font-urbanistBold text-typography-400'
              }`}
            >
              {item}
            </Text>
          </View>
        );
      },
      [currentCenterIndex]
    );

    return (
      <View style={{ width, height: WHEEL_HEIGHT }} className="relative">
        <View
          className="absolute left-0 right-0 bg-primary-0/20"
          style={highlightStyle}
          pointerEvents="none"
        />

        <ScrollView
          ref={scrollViewRef}
          showsVerticalScrollIndicator={false}
          decelerationRate="normal"
          bounces={true}
          bouncesZoom={false}
          onScroll={handleScroll}
          onScrollBeginDrag={handleScrollBeginDrag}
          onScrollEndDrag={handleScrollEndDrag}
          onMomentumScrollEnd={handleMomentumScrollEnd}
          scrollEventThrottle={16}
          contentContainerStyle={wheelItemStyle}
        >
          {data.map(renderItem)}
        </ScrollView>
      </View>
    );
  }
);

const WheelTimePicker = memo<WheelTimePickerProps>(({ value, onChange }) => {
  const wheelWidth = useMemo(() => {
    const screenWidth = Dimensions.get('window').width;
    return (screenWidth - WHEEL_CONFIG.WHEEL_PADDING) / 3;
  }, []);

  const handleHourChange = useCallback(
    (index: number) => {
      const hour = HOURS_DATA[index];
      if (hour !== undefined) {
        onChange({
          ...value,
          hour,
        });
      }
    },
    [value, onChange]
  );

  const handleMinuteChange = useCallback(
    (index: number) => {
      const minuteStr = MINUTES_DATA[index];
      if (minuteStr !== undefined) {
        onChange({
          ...value,
          minute: parseInt(minuteStr, 10),
        });
      }
    },
    [value, onChange]
  );

  const handlePeriodChange = useCallback(
    (index: number) => {
      const period = PERIODS_DATA[index];
      if (period !== undefined) {
        onChange({
          ...value,
          period,
        });
      }
    },
    [value, onChange]
  );

  const indices = useMemo(() => {
    const hourIndex = HOURS_DATA.indexOf(value.hour);
    const minuteIndex = MINUTES_DATA.indexOf(
      value.minute.toString().padStart(2, '0')
    );
    const periodIndex = PERIODS_DATA.indexOf(value.period);

    return {
      hour: hourIndex >= 0 ? hourIndex : 0,
      minute: minuteIndex >= 0 ? minuteIndex : 0,
      period: periodIndex >= 0 ? periodIndex : 0,
    };
  }, [value.hour, value.minute, value.period]);

  return (
    <View className="bg-white rounded-xl w-full">
      <View className="flex-row items-center justify-center gap-2">
        <View className="items-center">
          <WheelPicker
            data={HOURS_DATA}
            selectedIndex={indices.hour}
            onValueChange={handleHourChange}
            width={wheelWidth}
          />
        </View>

        <View className="items-center">
          <WheelPicker
            data={MINUTES_DATA}
            selectedIndex={indices.minute}
            onValueChange={handleMinuteChange}
            width={wheelWidth}
          />
        </View>

        <View className="items-center">
          <WheelPicker
            data={PERIODS_DATA}
            selectedIndex={indices.period}
            onValueChange={handlePeriodChange}
            width={wheelWidth}
          />
        </View>
      </View>
    </View>
  );
});

export default WheelTimePicker;
