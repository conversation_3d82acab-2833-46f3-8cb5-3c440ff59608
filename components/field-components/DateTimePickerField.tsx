import React, { useState, useCallback, useMemo, memo } from 'react';
import { View, Text, Pressable } from 'react-native';
import dayjs from 'dayjs';
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
} from '@/components/ui/actionsheet';
import { Button, ButtonText } from '@/components/ui/button';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { triggerHapticFeedback } from '@/utils';
import WheelTimePicker from './WheelTimePicker';
import SimpleCalendar from './SimpleCalendar';

export interface DateTimePickerFieldProps {
  value: Date | null;
  onChange: (date: Date) => void;
  mode: 'future' | 'past' | 'both';
  minDate?: Date;
  maxDate?: Date;
  disabled?: boolean;
  placeholder?: string;
  includeTime?: boolean;
  isInvalid?: boolean;
}

interface TimeValue {
  hour: number;
  minute: number;
  period: 'AM' | 'PM';
}

const DEFAULT_TIME: TimeValue = { hour: 12, minute: 0, period: 'PM' };

const convertDateToTimeValue = (date: Date): TimeValue => {
  const hour24 = date.getHours();
  const hour12 = hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24;
  const period = hour24 >= 12 ? 'PM' : 'AM';
  const roundedMinutes = Math.round(date.getMinutes() / 5) * 5;

  return {
    hour: hour12,
    minute: roundedMinutes,
    period,
  };
};

const convertTimeValueToDate = (date: Date, time: TimeValue): Date => {
  const newDate = new Date(date);
  let hour24 = time.hour;

  if (time.period === 'AM' && time.hour === 12) {
    hour24 = 0;
  } else if (time.period === 'PM' && time.hour !== 12) {
    hour24 = time.hour + 12;
  }

  newDate.setHours(hour24, time.minute, 0, 0);
  return newDate;
};

const DateTimePickerField = memo<DateTimePickerFieldProps>(
  ({
    value,
    onChange,
    mode,
    minDate,
    maxDate,
    disabled = false,
    placeholder = 'Select date',
    includeTime = false,
    isInvalid = false,
  }) => {
    const [isActionsheetVisible, setIsActionsheetVisible] = useState(false);
    const [selectedDate, setSelectedDate] = useState<Date | null>(value);

    const [selectedTime, setSelectedTime] = useState<TimeValue>(() => {
      if (value && includeTime) {
        return convertDateToTimeValue(value);
      }
      return DEFAULT_TIME;
    });

    const formatDisplayValue = useCallback(() => {
      if (!value || !(value instanceof Date) || isNaN(value.getTime())) {
        return placeholder;
      }
      return includeTime
        ? dayjs(value).format('MMM DD, YYYY h:mm A')
        : dayjs(value).format('MMM DD, YYYY');
    }, [value, placeholder, includeTime]);

    const handleDateSelect = useCallback((date: Date) => {
      setSelectedDate(date);
    }, []);

    const handleTimeChange = useCallback((time: TimeValue) => {
      setSelectedTime(time);
    }, []);

    const handleConfirm = useCallback(() => {
      if (!selectedDate) return;

      const finalDate = includeTime
        ? convertTimeValueToDate(selectedDate, selectedTime)
        : selectedDate;

      onChange(finalDate);
      setIsActionsheetVisible(false);
      triggerHapticFeedback();
    }, [selectedDate, selectedTime, onChange, includeTime]);

    const handleCancel = useCallback(() => {
      setSelectedDate(value);
      if (value && includeTime) {
        setSelectedTime(convertDateToTimeValue(value));
      }
      setIsActionsheetVisible(false);
    }, [value, includeTime]);

    const handleOpen = useCallback(() => {
      if (disabled) return;
      setIsActionsheetVisible(true);
    }, [disabled]);

    const triggerButtonStyle = useMemo(() => {
      const baseStyle =
        'flex-row items-center justify-between p-4 border rounded-lg';
      const backgroundStyle = disabled ? 'bg-gray-100' : 'bg-white';
      const borderStyle = isInvalid ? 'border-error-700' : 'border-gray-300';

      return `${baseStyle} ${borderStyle} ${backgroundStyle}`;
    }, [disabled, isInvalid]);

    const displayValueStyle = useMemo(
      () =>
        `text-base font-urbanistMedium ${
          value ? 'text-typography-800' : 'text-typography-500'
        }`,
      [value]
    );

    return (
      <View>
        <Pressable
          onPress={handleOpen}
          className={triggerButtonStyle}
          disabled={disabled}
        >
          <Text className={displayValueStyle}>{formatDisplayValue()}</Text>
          <Text className="text-typography-500 font-urbanistMedium">
            Tap to change
          </Text>
        </Pressable>

        <Actionsheet isOpen={isActionsheetVisible} onClose={handleCancel}>
          <ActionsheetBackdrop />
          <ActionsheetContent className="max-h-[80%]">
            <ActionsheetDragIndicatorWrapper>
              <ActionsheetDragIndicator />
            </ActionsheetDragIndicatorWrapper>

            <VStack className="space-y-6 p-4 w-full gap-2">
              <SimpleCalendar
                selectedDate={selectedDate}
                onDateSelect={handleDateSelect}
                minDate={minDate}
                maxDate={maxDate}
                mode={mode}
              />

              {includeTime && (
                <WheelTimePicker
                  value={selectedTime}
                  onChange={handleTimeChange}
                />
              )}

              <HStack className="space-x-3 pt-4 gap-2">
                <Button
                  variant="outline"
                  action="secondary"
                  className="flex-1"
                  onPress={handleCancel}
                >
                  <ButtonText className="font-urbanistSemiBold">
                    Cancel
                  </ButtonText>
                </Button>
                <Button
                  className="flex-1 bg-primary-0"
                  onPress={handleConfirm}
                  disabled={!selectedDate}
                >
                  <ButtonText className="font-urbanistSemiBold">
                    Apply
                  </ButtonText>
                </Button>
              </HStack>
            </VStack>
          </ActionsheetContent>
        </Actionsheet>
      </View>
    );
  }
);

DateTimePickerField.displayName = 'DateTimePickerField';

export default DateTimePickerField;
