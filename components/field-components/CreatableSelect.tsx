import React, { useState, useMemo, useCallback, memo } from 'react';
import { View, Text, Pressable, ScrollView } from 'react-native';
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
  ActionsheetItem,
  ActionsheetItemText,
} from '@/components/ui/actionsheet';
import { ChevronDownIcon, AddIcon, Icon } from '@/components/ui/icon';
import { ButtonSpinner } from '@/components/ui/button';
import { triggerHapticFeedback } from '@/utils';
import SearchBar from '@/components/k-components/SearchBar';
import { useDebounce } from '@/hooks/useDebounce';
import NoDataFound from '../k-components/NoDataFound';

export interface CreatableOption {
  label: string;
  value: string;
}

interface CreatableSelectProps {
  options: CreatableOption[];
  value?: string;
  placeholder?: string;
  onValueChange: (value: string) => void;
  onCreateOption?: (
    label: string
  ) => CreatableOption | Promise<CreatableOption>;
  disabled?: boolean;
  className?: string;
  createOptionText?: string;
  noOptionsText?: string;
  searchPlaceholder?: string;
  isInvalid?: boolean;
}

const STYLES = {
  trigger: {
    base: 'flex-row items-center justify-between w-full px-4 py-3 rounded-md border',
    enabled: 'bg-background-0',
    disabled: 'opacity-50 bg-background-50',
    borderDefault: 'border-outline-300',
    borderError: 'border-error-700',
  },
  text: {
    triggerBase: 'font-urbanistMedium text-base',
    triggerSelected: 'text-typography-900',
    triggerPlaceholder: 'text-typography-500',
    optionBase: 'text-base font-urbanistMedium py-1',
    optionSelected: 'text-primary-0 font-urbanistBold',
    optionUnselected: 'text-typography-900',
    create: 'text-base font-urbanistSemiBold text-primary-0',
  },
  create: {
    container: 'mb-5',
    button:
      'flex-row items-center justify-center rounded-lg border-b border-primary-0',
    buttonDisabled: 'opacity-50',
  },
} as const;

interface OptionItemProps {
  option: CreatableOption;
  isSelected: boolean;
  onPress: (value: string) => void;
}

const OptionItem = memo<OptionItemProps>(({ option, isSelected, onPress }) => {
  const labelClassName = useMemo(
    () =>
      [
        STYLES.text.optionBase,
        isSelected ? STYLES.text.optionSelected : STYLES.text.optionUnselected,
      ].join(' '),
    [isSelected]
  );

  const containerClassName = useMemo(
    () =>
      ['rounded-lg active:bg-gray-100', isSelected ? 'bg-gray-100' : ''].join(
        ' '
      ),
    [isSelected]
  );

  const handlePress = useCallback(() => {
    triggerHapticFeedback();
    onPress(option.value);
  }, [onPress, option.value]);

  return (
    <ActionsheetItem onPress={handlePress} className={containerClassName}>
      <ActionsheetItemText className={labelClassName}>
        {option.label}
      </ActionsheetItemText>
    </ActionsheetItem>
  );
});

OptionItem.displayName = 'OptionItem';

const DEFAULT_PROPS = {
  placeholder: 'Select or create',
  disabled: false,
  className: '',
  createOptionText: 'Create',
  noOptionsText: 'No options found',
  searchPlaceholder: 'Search or type to create...',
} as const;

const CreatableSelectComponent: React.FC<CreatableSelectProps> = ({
  options,
  value,
  placeholder = DEFAULT_PROPS.placeholder,
  onValueChange,
  onCreateOption,
  disabled = DEFAULT_PROPS.disabled,
  className = DEFAULT_PROPS.className,
  createOptionText = DEFAULT_PROPS.createOptionText,
  noOptionsText = DEFAULT_PROPS.noOptionsText,
  searchPlaceholder = DEFAULT_PROPS.searchPlaceholder,
  isInvalid = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  const debouncedSearchQuery = useDebounce(searchQuery, 250);

  const selectedOption = useMemo(
    () => options.find((opt) => opt.value === value),
    [options, value]
  );

  const filteredOptions = useMemo(() => {
    if (!debouncedSearchQuery.trim()) return options;

    return options.filter((option) =>
      option.label.toLowerCase().includes(debouncedSearchQuery.toLowerCase())
    );
  }, [options, debouncedSearchQuery]);

  const canCreateOption = useMemo(() => {
    if (!debouncedSearchQuery.trim() || !onCreateOption) return false;

    // Check if exact match exists
    const exactMatch = options.some(
      (option) =>
        option.label.toLowerCase() === debouncedSearchQuery.toLowerCase()
    );

    return !exactMatch;
  }, [options, debouncedSearchQuery, onCreateOption]);

  const triggerClassName = useMemo(() => {
    const classes = [
      STYLES.trigger.base,
      disabled ? STYLES.trigger.disabled : STYLES.trigger.enabled,
      isInvalid ? STYLES.trigger.borderError : STYLES.trigger.borderDefault,
      className,
    ].filter(Boolean);
    return classes.join(' ');
  }, [disabled, isInvalid, className]);

  const triggerTextClassName = useMemo(() => {
    const classes = [
      STYLES.text.triggerBase,
      selectedOption
        ? STYLES.text.triggerSelected
        : STYLES.text.triggerPlaceholder,
    ];
    return classes.join(' ');
  }, [selectedOption]);

  const handleOpen = useCallback(() => {
    if (disabled) return;
    setIsOpen(true);
  }, [disabled]);

  const handleClose = useCallback(() => {
    setIsOpen(false);
  }, []);

  const handleValueChange = useCallback(
    (newValue: string) => {
      onValueChange(newValue);
      handleClose();
    },
    [onValueChange, handleClose]
  );

  const handleCreateOption = useCallback(async () => {
    if (!onCreateOption || !debouncedSearchQuery.trim() || isCreating) return;

    setIsCreating(true);
    try {
      const newOption = await onCreateOption(debouncedSearchQuery.trim());
      handleValueChange(newOption.value);
    } catch (error) {
      console.error('Error creating option:', error);
    } finally {
      setIsCreating(false);
    }
  }, [onCreateOption, debouncedSearchQuery, isCreating, handleValueChange]);

  const handleSearchChange = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const scrollViewStyle = useMemo(
    () => ({
      minHeight: 200,
      paddingBottom: canCreateOption ? 80 : 20,
    }),
    [canCreateOption]
  );

  return (
    <>
      <Pressable
        onPress={handleOpen}
        className={triggerClassName}
        disabled={disabled}
      >
        <Text className={triggerTextClassName}>
          {selectedOption?.label || placeholder}
        </Text>
        <Icon as={ChevronDownIcon} className="text-typography-500" size="sm" />
      </Pressable>

      <Actionsheet isOpen={isOpen} onClose={handleClose}>
        <ActionsheetBackdrop />
        <ActionsheetContent className="min-h-[50vh] max-h-[70vh]">
          <ActionsheetDragIndicatorWrapper>
            <ActionsheetDragIndicator />
          </ActionsheetDragIndicatorWrapper>

          <View className="w-full px-4 mt-4">
            <SearchBar
              key={isOpen ? 'open' : 'closed'}
              value=""
              onDebouncedChange={handleSearchChange}
              placeholder={searchPlaceholder}
            />
          </View>

          <ScrollView
            className="w-full flex-1"
            showsVerticalScrollIndicator={false}
            contentContainerStyle={scrollViewStyle}
          >
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option) => (
                <OptionItem
                  key={option.value}
                  option={option}
                  isSelected={option.value === value}
                  onPress={handleValueChange}
                />
              ))
            ) : (
              <NoDataFound
                title={noOptionsText}
                subtitle={`Start typing to create a new option.`}
                className="min-h-[30vh]"
              />
            )}
          </ScrollView>

          {canCreateOption && (
            <View className={STYLES.create.container}>
              <Pressable
                onPress={handleCreateOption}
                disabled={isCreating}
                className={`${STYLES.create.button} ${
                  isCreating ? STYLES.create.buttonDisabled : ''
                }`}
              >
                {isCreating ? (
                  <ButtonSpinner size="small" className="text-primary-0 mr-2" />
                ) : (
                  <Icon
                    as={AddIcon}
                    className="text-primary-0 mr-2"
                    size="sm"
                  />
                )}
                <Text className={STYLES.text.create}>
                  {isCreating
                    ? 'Creating...'
                    : `${createOptionText} "${debouncedSearchQuery.trim()}"`}
                </Text>
              </Pressable>
            </View>
          )}
        </ActionsheetContent>
      </Actionsheet>
    </>
  );
};

CreatableSelectComponent.displayName = 'CreatableSelect';

const CreatableSelect = memo(CreatableSelectComponent);

export default CreatableSelect;
