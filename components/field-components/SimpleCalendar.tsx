import React, { useState, useCallback, useMemo, memo } from 'react';
import { View, Text, Pressable } from 'react-native';
import dayjs from 'dayjs';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Icon } from '@/components/ui/icon';
import { ChevronLeftIcon, ChevronRightIcon } from 'lucide-react-native';
import { triggerHapticFeedback } from '@/utils';

export interface SimpleCalendarProps {
  selectedDate: Date | null;
  onDateSelect: (date: Date) => void;
  minDate?: Date;
  maxDate?: Date;
  mode: 'future' | 'past' | 'both';
}

interface CalendarState {
  currentMonth: dayjs.Dayjs;
  today: dayjs.Dayjs;
  effectiveMinDate: dayjs.Dayjs | null;
  effectiveMaxDate: dayjs.Dayjs | null;
}

const WEEKDAYS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'] as const;

const useCalendarState = (
  selectedDate: Date | null,
  minDate: Date | undefined,
  maxDate: Date | undefined,
  mode: 'future' | 'past' | 'both'
) => {
  const today = useMemo(() => dayjs(), []);

  const [currentMonth, setCurrentMonth] = useState(() => {
    if (selectedDate) return dayjs(selectedDate);

    if (
      mode === 'future' &&
      minDate &&
      dayjs(minDate).isAfter(today, 'month')
    ) {
      return dayjs(minDate);
    }

    if (mode === 'past' && maxDate && dayjs(maxDate).isBefore(today, 'month')) {
      return dayjs(maxDate);
    }

    return today;
  });

  const effectiveMinDate = useMemo(() => {
    if (mode === 'future') {
      return minDate && dayjs(minDate).isAfter(today) ? dayjs(minDate) : today;
    }
    return minDate ? dayjs(minDate) : null;
  }, [mode, minDate, today]);

  const effectiveMaxDate = useMemo(() => {
    if (mode === 'past') {
      return maxDate && dayjs(maxDate).isBefore(today) ? dayjs(maxDate) : today;
    }
    return maxDate ? dayjs(maxDate) : null;
  }, [mode, maxDate, today]);

  React.useEffect(() => {
    if (
      mode === 'future' &&
      minDate &&
      dayjs(minDate).isAfter(today, 'month')
    ) {
      if (currentMonth.isBefore(dayjs(minDate), 'month')) {
        setCurrentMonth(dayjs(minDate));
      }
    }

    if (mode === 'past' && maxDate && dayjs(maxDate).isBefore(today, 'month')) {
      if (currentMonth.isAfter(dayjs(maxDate), 'month')) {
        setCurrentMonth(dayjs(maxDate));
      }
    }
  }, [minDate, maxDate, mode, currentMonth, today]);

  return {
    currentMonth,
    setCurrentMonth,
    today,
    effectiveMinDate,
    effectiveMaxDate,
  };
};

const useCalendarDays = (currentMonth: dayjs.Dayjs) => {
  return useMemo(() => {
    const startOfMonth = currentMonth.startOf('month');
    const endOfMonth = currentMonth.endOf('month');
    const startOfCalendar = startOfMonth.startOf('week');
    const endOfCalendar = endOfMonth.endOf('week');

    const calendarDays: dayjs.Dayjs[] = [];
    let day = startOfCalendar;

    while (day.isBefore(endOfCalendar) || day.isSame(endOfCalendar, 'day')) {
      calendarDays.push(day);
      day = day.add(1, 'day');
    }

    return calendarDays;
  }, [currentMonth]);
};

const CalendarHeader = memo<{
  currentMonth: dayjs.Dayjs;
  onPreviousMonth: () => void;
  onNextMonth: () => void;
  canNavigatePrevious: boolean;
  canNavigateNext: boolean;
}>(
  ({
    currentMonth,
    onPreviousMonth,
    onNextMonth,
    canNavigatePrevious,
    canNavigateNext,
  }) => (
    <HStack className="justify-between items-center px-4 mb-2">
      <Pressable
        onPress={onPreviousMonth}
        className="p-2"
        disabled={!canNavigatePrevious}
      >
        <Icon
          as={ChevronLeftIcon}
          size="md"
          className={canNavigatePrevious ? 'text-primary-600' : 'text-gray-300'}
        />
      </Pressable>
      <Text className="text-lg font-urbanistBold text-typography-900">
        {currentMonth.format('MMMM YYYY')}
      </Text>
      <Pressable
        onPress={onNextMonth}
        className="p-2"
        disabled={!canNavigateNext}
      >
        <Icon
          as={ChevronRightIcon}
          size="md"
          className={canNavigateNext ? 'text-primary-600' : 'text-gray-300'}
        />
      </Pressable>
    </HStack>
  )
);

const WeekdayHeaders = memo(() => (
  <HStack className="justify-between px-2">
    {WEEKDAYS.map((day) => (
      <View key={day} className="w-10 h-8 justify-center items-center">
        <Text className="text-sm font-urbanistMedium text-typography-600">
          {day}
        </Text>
      </View>
    ))}
  </HStack>
));

const CalendarDay = memo<{
  date: dayjs.Dayjs;
  isSelected: boolean;
  isToday: boolean;
  isCurrentMonth: boolean;
  isDisabled: boolean;
  onPress: () => void;
}>(({ date, isSelected, isToday, isCurrentMonth, isDisabled, onPress }) => (
  <Pressable
    onPress={onPress}
    disabled={isDisabled}
    className={`w-10 h-10 justify-center items-center rounded-lg ${
      isSelected ? 'bg-primary-0' : 'bg-transparent'
    }`}
  >
    <Text
      className={`text-base font-urbanistMedium ${
        isSelected
          ? 'text-white'
          : isDisabled
          ? 'text-gray-300'
          : isToday
          ? 'text-primary-0 !font-urbanistSemiBold'
          : isCurrentMonth
          ? 'text-typography-800'
          : 'text-gray-400'
      }`}
    >
      {date.format('D')}
    </Text>
  </Pressable>
));

const SimpleCalendar = memo<SimpleCalendarProps>(
  ({ selectedDate, onDateSelect, minDate, maxDate, mode }) => {
    const {
      currentMonth,
      setCurrentMonth,
      today,
      effectiveMinDate,
      effectiveMaxDate,
    } = useCalendarState(selectedDate, minDate, maxDate, mode);

    const calendarDays = useCalendarDays(currentMonth);

    const isDateDisabled = useCallback(
      (date: dayjs.Dayjs) => {
        if (effectiveMinDate && date.isBefore(effectiveMinDate, 'day'))
          return true;
        if (effectiveMaxDate && date.isAfter(effectiveMaxDate, 'day'))
          return true;
        return false;
      },
      [effectiveMinDate, effectiveMaxDate]
    );

    const isDateSelected = useCallback(
      (date: dayjs.Dayjs) => dayjs(selectedDate).isSame(date, 'day'),
      [selectedDate]
    );

    const isToday = useCallback(
      (date: dayjs.Dayjs) => date.isSame(today, 'day'),
      [today]
    );

    const isCurrentMonth = useCallback(
      (date: dayjs.Dayjs) => date.isSame(currentMonth, 'month'),
      [currentMonth]
    );

    const handleDatePress = useCallback(
      (date: dayjs.Dayjs) => {
        if (!isDateDisabled(date)) {
          triggerHapticFeedback();
          onDateSelect(date.toDate());
        }
      },
      [isDateDisabled, onDateSelect]
    );

    const goToPreviousMonth = useCallback(() => {
      setCurrentMonth((prev) => {
        const newMonth = prev.subtract(1, 'month');
        if (effectiveMinDate && newMonth.isBefore(effectiveMinDate, 'month')) {
          return prev;
        }
        return newMonth;
      });
    }, [effectiveMinDate]);

    const goToNextMonth = useCallback(() => {
      setCurrentMonth((prev) => {
        const newMonth = prev.add(1, 'month');
        if (effectiveMaxDate && newMonth.isAfter(effectiveMaxDate, 'month')) {
          return prev;
        }
        return newMonth;
      });
    }, [effectiveMaxDate]);

    const canNavigatePrevious = useMemo(
      () =>
        !effectiveMinDate || !currentMonth.isSame(effectiveMinDate, 'month'),
      [effectiveMinDate, currentMonth]
    );

    const canNavigateNext = useMemo(
      () =>
        !effectiveMaxDate || !currentMonth.isSame(effectiveMaxDate, 'month'),
      [effectiveMaxDate, currentMonth]
    );

    const calendarWeeks = useMemo(
      () => Array.from({ length: Math.ceil(calendarDays.length / 7) }),
      [calendarDays.length]
    );

    return (
      <VStack className="space-y-4">
        <CalendarHeader
          currentMonth={currentMonth}
          onPreviousMonth={goToPreviousMonth}
          onNextMonth={goToNextMonth}
          canNavigatePrevious={canNavigatePrevious}
          canNavigateNext={canNavigateNext}
        />

        <WeekdayHeaders />

        <View className="px-2">
          {calendarWeeks.map((_, weekIndex) => (
            <HStack key={weekIndex} className="justify-between mb-1">
              {calendarDays
                .slice(weekIndex * 7, (weekIndex + 1) * 7)
                .map((date, dayIndex) => (
                  <CalendarDay
                    key={dayIndex}
                    date={date}
                    isSelected={isDateSelected(date)}
                    isToday={isToday(date)}
                    isCurrentMonth={isCurrentMonth(date)}
                    isDisabled={isDateDisabled(date)}
                    onPress={() => handleDatePress(date)}
                  />
                ))}
            </HStack>
          ))}
        </View>
      </VStack>
    );
  }
);

SimpleCalendar.displayName = 'SimpleCalendar';

export default SimpleCalendar;
