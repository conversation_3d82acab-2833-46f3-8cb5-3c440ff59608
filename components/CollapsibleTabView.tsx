import React, { useMemo, useState, useCallback } from 'react';
import { Dimensions, RefreshControl } from 'react-native';
import {
  Tabs,
  MaterialTabBar,
  useCollapsibleStyle,
} from 'react-native-collapsible-tab-view';
import { TabColors } from '@/constants/Colors';
import { CollapsibleTabViewProps, TabBarConfig, TabItem } from '@/types/tab';

const defaultTabBarConfig: TabBarConfig = {
  scrollEnabled: false,
  fontFamily: 'Urbanist_600SemiBold',
  fontWeight: '600',
  textTransform: 'capitalize',
  textAlign: 'center',
  paddingVertical: 8,
  paddingHorizontal: 0,
  margin: 0,
};

// Component to handle pull-to-refresh for individual tabs
function TabScrollView<T>({
  tab,
  data,
  showsVerticalScrollIndicator,
  enablePullToRefresh,
  onRefresh,
  refreshing,
}: {
  tab: TabItem<T>;
  data: T;
  showsVerticalScrollIndicator: boolean;
  enablePullToRefresh: boolean;
  onRefresh?: () => Promise<void> | void;
  refreshing: boolean;
}) {
  const { progressViewOffset } = useCollapsibleStyle();
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = useCallback(async () => {
    if (!enablePullToRefresh) return;

    setIsRefreshing(true);

    try {
      const refreshFunctions: Array<() => Promise<void> | void> = [];
      if (onRefresh) {
        refreshFunctions.push(onRefresh);
      }
      if (tab.onRefresh) {
        refreshFunctions.push(tab.onRefresh);
      }

      if (refreshFunctions.length > 0) {
        await Promise.all(
          refreshFunctions.map(async (fn) => {
            const result = fn();
            if (result instanceof Promise) {
              await result;
            }
          })
        );
      }
    } catch (error) {
      console.error('Error during refresh:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [enablePullToRefresh, onRefresh, tab.onRefresh]);

  const refreshControl = enablePullToRefresh ? (
    <RefreshControl
      refreshing={refreshing || isRefreshing}
      onRefresh={handleRefresh}
      progressViewOffset={progressViewOffset}
    />
  ) : undefined;

  return (
    <Tabs.ScrollView
      showsVerticalScrollIndicator={showsVerticalScrollIndicator}
      refreshControl={refreshControl}
    >
      {tab.render(data)}
    </Tabs.ScrollView>
  );
}

export default function CollapsibleTabView<T = any>({
  data,
  tabs,
  tabBarConfig = {},
  lazy = false,
  renderHeader,
  headerHeight,
  initialTabName,
  onTabChange,
  showsVerticalScrollIndicator = false,
  tabFilter,
  snapThreshold,
  revealHeaderOnScroll,
  cancelLazyFadeIn,
  allowHeaderOverscroll,
  minHeaderHeight,
  pagerProps,
  enablePullToRefresh = false,
  onRefresh,
  refreshing = false,
}: CollapsibleTabViewProps<T>) {
  const mergedTabBarConfig = { ...defaultTabBarConfig, ...tabBarConfig };

  const visibleTabs = useMemo(() => {
    if (tabFilter) {
      return tabs.filter((tab) => tabFilter(tab, data));
    }
    return tabs;
  }, [tabs, data, tabFilter]);

  const screenWidth = Dimensions.get('window').width;
  const calculatedTabWidth =
    mergedTabBarConfig.minWidth || screenWidth / visibleTabs.length;

  const containerProps = {
    lazy,
    ...(initialTabName && { initialTabName }),
    ...(onTabChange && { onTabChange }),
    ...(renderHeader && { renderHeader: () => renderHeader() }),
    ...(headerHeight && { headerHeight }),
    ...(snapThreshold !== undefined && { snapThreshold }),
    ...(revealHeaderOnScroll !== undefined && { revealHeaderOnScroll }),
    ...(cancelLazyFadeIn !== undefined && { cancelLazyFadeIn }),
    ...(allowHeaderOverscroll !== undefined && { allowHeaderOverscroll }),
    ...(minHeaderHeight !== undefined && { minHeaderHeight }),
    ...(pagerProps && { pagerProps }),
  };

  return (
    <Tabs.Container
      {...containerProps}
      renderTabBar={(props) => (
        <MaterialTabBar
          {...props}
          scrollEnabled={mergedTabBarConfig.scrollEnabled}
          indicatorStyle={{
            backgroundColor: TabColors.active,
            height: 3,
          }}
          activeColor={TabColors.active}
          inactiveColor={TabColors.inactive}
          labelStyle={{
            textAlign: mergedTabBarConfig.textAlign,
            fontFamily: mergedTabBarConfig.fontFamily,
            fontWeight: mergedTabBarConfig.fontWeight as any,
            minWidth: calculatedTabWidth,
            margin: mergedTabBarConfig.margin,
            paddingHorizontal: mergedTabBarConfig.paddingHorizontal,
            paddingVertical: mergedTabBarConfig.paddingVertical,
            textTransform: mergedTabBarConfig.textTransform,
          }}
          tabStyle={mergedTabBarConfig.tabStyle}
          contentContainerStyle={mergedTabBarConfig.contentContainerStyle}
          style={{
            borderBottomWidth: 1,
            borderBottomColor: TabColors.border,
          }}
        />
      )}
    >
      {visibleTabs.map((tab) => (
        <Tabs.Tab name={tab.id} key={tab.id}>
          <TabScrollView
            tab={tab}
            data={data}
            showsVerticalScrollIndicator={showsVerticalScrollIndicator}
            enablePullToRefresh={enablePullToRefresh}
            onRefresh={onRefresh}
            refreshing={refreshing}
          />
        </Tabs.Tab>
      ))}
    </Tabs.Container>
  );
}
