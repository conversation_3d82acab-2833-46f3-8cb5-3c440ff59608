import React, { useState } from 'react';
import { View, Text as RNText, TouchableOpacity } from 'react-native';
import { CopyIcon, CheckIcon } from 'lucide-react-native';
import * as Clipboard from 'expo-clipboard';
import clsx from 'clsx';
import type { LucideIcon } from 'lucide-react-native';
import { Icon } from '../ui/icon';
import { some, isEmpty } from 'lodash';

interface InfoListItemProps {
  icon: LucideIcon;
  value?: string;
  numberOfLines?: number;
  copyable?: boolean;
}

const InfoListItem: React.FC<InfoListItemProps> = ({
  icon,
  value,
  numberOfLines = 1,
  copyable = false,
}) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    if (value) {
      await Clipboard.setStringAsync(value);
      setCopied(true);
      setTimeout(() => setCopied(false), 3000);
    }
  };

  if (isEmpty(value)) return null;

  return (
    <View className="flex flex-row items-center gap-1 py-0.5">
      <View className="flex-row items-center space-x-2 flex-1 gap-2">
        <Icon size="sm" className="text-primary-0" as={icon} />
        <RNText
          numberOfLines={numberOfLines}
          ellipsizeMode="tail"
          className="text-typography-600 font-urbanistSemiBold text-sm"
        >
          {value}
        </RNText>
        {copyable && value !== 'N/A' && (
          <TouchableOpacity onPress={handleCopy} className="ml-4">
            {copied ? (
              <Icon className="text-typography-800" as={CheckIcon} size="sm" />
            ) : (
              <Icon className="text-typography-800" as={CopyIcon} size="sm" />
            )}
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};
interface InfoItem {
  icon: LucideIcon;
  value?: string;
  numberOfLines?: number;
  copyable?: boolean;
}

interface ListInfoCardProps {
  title: string;
  items: InfoItem[];
  headerRight?: React.ReactNode;
  className?: string;
}

const ListInfoCard: React.FC<ListInfoCardProps> = ({
  title,
  items,
  headerRight,
  className,
}) => {
  const hasAnyValue = some(items, (item) => !isEmpty(item.value));

  if (!hasAnyValue) return null;

  return (
    <View
      className={clsx(
        'flex-1 bg-white border border-gray-200 rounded-lg p-4 shadow-sm',
        className
      )}
    >
      <View className="flex-row items-center justify-between mb-2">
        <RNText
          className="font-urbanistExtraBold text-typography-700 text-xl"
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {title}
        </RNText>
        {headerRight && <View>{headerRight}</View>}
      </View>

      <View className="border-t border-dashed border-gray-300 mb-1.5" />

      <View className="flex-col gap-1 mt-1">
        {items.map((item, idx) => (
          <InfoListItem
            key={idx}
            icon={item.icon}
            value={item.value}
            numberOfLines={item.numberOfLines}
            copyable={item.copyable}
          />
        ))}
      </View>
    </View>
  );
};

export default ListInfoCard;
