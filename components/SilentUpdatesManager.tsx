import React, { useEffect } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import {
  useAutoSilentUpdates,
  useSilentDownloadUpdates,
} from '@/lib/hooks/useUpdates';

interface SilentUpdatesManagerProps {
  /**
   * Update strategy:
   * - 'auto': Downloads and installs immediately (app restarts automatically)
   * - 'download-only': Downloads silently, installs on next app restart
   * - 'download-install-on-background': Downloads silently, installs when app goes to background
   */
  strategy?: 'auto' | 'download-only' | 'download-install-on-background';

  /**
   * How often to check for updates (in minutes)
   * Default: 30 minutes
   */
  checkIntervalMinutes?: number;

  /**
   * Callbacks for update events
   */
  onUpdateStart?: () => void;
  onUpdateDownloaded?: () => void;
  onUpdateInstalled?: () => void;
  onUpdateError?: (error: string) => void;

  /**
   * Whether to log update activities to console
   */
  enableLogging?: boolean;
}

export function SilentUpdatesManager({
  strategy = 'download-install-on-background',
  checkIntervalMinutes = 30,
  onUpdateStart,
  onUpdateDownloaded,
  onUpdateInstalled,
  onUpdateError,
  enableLogging = __DEV__,
}: SilentUpdatesManagerProps) {
  const checkInterval = checkIntervalMinutes * 60 * 1000;

  // Strategy 1: Completely automatic updates
  useAutoSilentUpdates(
    strategy === 'auto'
      ? {
          checkInterval,
          onUpdateStart: () => {
            if (enableLogging)
              console.log('🔄 Auto-update: Starting update process');
            onUpdateStart?.();
          },
          onUpdateComplete: () => {
            if (enableLogging)
              console.log('✅ Auto-update: Update completed, restarting app');
            onUpdateInstalled?.();
          },
          onUpdateError: (error) => {
            if (enableLogging) console.error('❌ Auto-update error:', error);
            onUpdateError?.(error);
          },
        }
      : {}
  );

  // Strategy 2 & 3: Download silently, install based on strategy
  const { updateReady, installUpdate } = useSilentDownloadUpdates(
    strategy !== 'auto'
      ? {
          checkInterval,
          onUpdateDownloaded: () => {
            if (enableLogging)
              console.log('📥 Silent download: Update downloaded and ready');
            onUpdateDownloaded?.();

            // For download-only strategy, just wait for next app restart
            if (strategy === 'download-only') {
              if (enableLogging)
                console.log('⏳ Update will be applied on next app restart');
            }
          },
          onDownloadError: (error) => {
            if (enableLogging)
              console.error('❌ Silent download error:', error);
            onUpdateError?.(error);
          },
        }
      : {}
  );

  // Strategy 3: Install when app goes to background
  useEffect(() => {
    if (strategy !== 'download-install-on-background' || !updateReady) return;

    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'background' && updateReady) {
        if (enableLogging)
          console.log('📱 App going to background, installing update...');
        onUpdateInstalled?.();
        installUpdate();
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange
    );
    return () => subscription?.remove();
  }, [strategy, updateReady, installUpdate, enableLogging, onUpdateInstalled]);

  // This component doesn't render anything - it just manages updates in the background
  return null;
}

/**
 * Simple auto-update component that handles everything automatically
 * Just add this to your root component and forget about it
 */
export function AutoUpdater(
  props: Omit<SilentUpdatesManagerProps, 'strategy'>
) {
  return <SilentUpdatesManager {...props} strategy="auto" />;
}

/**
 * Background downloader that installs updates when app goes to background
 * Good balance between automatic updates and user control
 */
export function BackgroundUpdater(
  props: Omit<SilentUpdatesManagerProps, 'strategy'>
) {
  return (
    <SilentUpdatesManager
      {...props}
      strategy="download-install-on-background"
    />
  );
}

/**
 * Silent downloader that only downloads updates, installs on next app restart
 * Most conservative approach - updates are applied when user naturally restarts the app
 */
export function SilentDownloader(
  props: Omit<SilentUpdatesManagerProps, 'strategy'>
) {
  return <SilentUpdatesManager {...props} strategy="download-only" />;
}

export default SilentUpdatesManager;
