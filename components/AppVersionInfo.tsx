import React, { useState, useRef } from 'react';
import { View, Pressable } from 'react-native';
import { Text } from '@/components/ui/text';
import Constants from 'expo-constants';
import { router } from 'expo-router';

function AppVersionInfo() {
  const appVersion = Constants.expoConfig?.version || '0.1.0';
  const [tapCount, setTapCount] = useState(0);
  const tapTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleVersionTap = () => {
    const newTapCount = tapCount + 1;
    setTapCount(newTapCount);

    // Clear existing timeout
    if (tapTimeoutRef.current) {
      clearTimeout(tapTimeoutRef.current);
    }

    // Reset tap count after 2 seconds of no taps
    tapTimeoutRef.current = setTimeout(() => {
      setTapCount(0);
    }, 2000);

    // Open debug screen after 10 taps
    if (newTapCount >= 10) {
      setTapCount(0);
      if (tapTimeoutRef.current) {
        clearTimeout(tapTimeoutRef.current);
      }
      router.push('/debug-updates');
    }
  };

  return (
    <View className="items-center py-6 absolute bottom-20 left-0 right-0">
      <Pressable onPress={handleVersionTap}>
        <Text className="text-sm text-gray-500 font-urbanist">
          App Version {appVersion}
        </Text>
      </Pressable>
    </View>
  );
}

export default AppVersionInfo;
