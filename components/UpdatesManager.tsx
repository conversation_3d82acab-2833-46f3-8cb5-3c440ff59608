import React from 'react';
import { Alert } from 'react-native';
import { Box } from '@/components/ui/box';
import { Button, ButtonText } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { useUpdates, useBackgroundUpdates } from '@/lib/hooks/useUpdates';

interface UpdatesManagerProps {
  showDebugInfo?: boolean;
}

export function UpdatesManager({ showDebugInfo = false }: UpdatesManagerProps) {
  const {
    isChecking,
    isDownloading,
    updateInfo,
    error,
    checkForUpdates,
    downloadAndInstall,
    currentUpdateInfo,
  } = useUpdates();

  const { hasUpdate, applyUpdate } = useBackgroundUpdates();

  const handleCheckForUpdates = async () => {
    await checkForUpdates();
    
    if (updateInfo?.isUpdateAvailable) {
      Alert.alert(
        'Update Available',
        'A new version is available. Would you like to download and install it?',
        [
          { text: 'Later', style: 'cancel' },
          { 
            text: 'Update Now', 
            onPress: downloadAndInstall 
          },
        ]
      );
    } else {
      Alert.alert('No Updates', 'You are running the latest version.');
    }
  };

  const handleApplyBackgroundUpdate = () => {
    Alert.alert(
      'Update Ready',
      'An update has been downloaded and is ready to install. The app will restart.',
      [
        { text: 'Later', style: 'cancel' },
        { 
          text: 'Restart Now', 
          onPress: applyUpdate 
        },
      ]
    );
  };

  if (__DEV__ && !showDebugInfo) {
    return null; // Don't show in development unless explicitly requested
  }

  return (
    <Box className="p-4 space-y-4">
      {/* Update Status */}
      <Box className="space-y-2">
        <Text className="text-lg font-semibold">App Updates</Text>
        
        {error && (
          <Box className="p-3 bg-red-100 rounded-lg">
            <Text className="text-red-800">{error}</Text>
          </Box>
        )}

        {hasUpdate && (
          <Box className="p-3 bg-green-100 rounded-lg">
            <Text className="text-green-800 mb-2">
              Update downloaded and ready to install!
            </Text>
            <Button 
              size="sm" 
              onPress={handleApplyBackgroundUpdate}
              className="bg-green-600"
            >
              <ButtonText>Restart & Update</ButtonText>
            </Button>
          </Box>
        )}
      </Box>

      {/* Action Buttons */}
      <Box className="space-y-2">
        <Button 
          onPress={handleCheckForUpdates}
          disabled={isChecking || isDownloading}
          className="bg-blue-600"
        >
          <ButtonText>
            {isChecking ? 'Checking...' : 'Check for Updates'}
          </ButtonText>
        </Button>

        {updateInfo?.isUpdateAvailable && (
          <Button 
            onPress={downloadAndInstall}
            disabled={isDownloading}
            className="bg-green-600"
          >
            <ButtonText>
              {isDownloading ? 'Downloading...' : 'Download & Install'}
            </ButtonText>
          </Button>
        )}
      </Box>

      {/* Debug Info */}
      {showDebugInfo && (
        <Box className="mt-4 p-3 bg-gray-100 rounded-lg">
          <Text className="font-semibold mb-2">Debug Info:</Text>
          <Text className="text-sm">Update ID: {currentUpdateInfo.updateId || 'N/A'}</Text>
          <Text className="text-sm">Channel: {currentUpdateInfo.channel || 'N/A'}</Text>
          <Text className="text-sm">Runtime Version: {currentUpdateInfo.runtimeVersion || 'N/A'}</Text>
          <Text className="text-sm">Embedded Launch: {currentUpdateInfo.isEmbeddedLaunch ? 'Yes' : 'No'}</Text>
          <Text className="text-sm">Emergency Launch: {currentUpdateInfo.isEmergencyLaunch ? 'Yes' : 'No'}</Text>
          
          {updateInfo && (
            <>
              <Text className="text-sm mt-2">Update Available: {updateInfo.isUpdateAvailable ? 'Yes' : 'No'}</Text>
              <Text className="text-sm">Update Pending: {updateInfo.isUpdatePending ? 'Yes' : 'No'}</Text>
            </>
          )}
        </Box>
      )}
    </Box>
  );
}

export default UpdatesManager;
