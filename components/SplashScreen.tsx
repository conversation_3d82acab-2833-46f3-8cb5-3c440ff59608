import React, { useEffect, useRef } from 'react';
import { View, Animated, Dimensions, StyleSheet } from 'react-native';
import Svg, { Rect, Path, Ellipse, G } from 'react-native-svg';
import { StatusBar } from 'expo-status-bar';

const AnimatedPath = Animated.createAnimatedComponent(Path);
const AnimatedEllipse = Animated.createAnimatedComponent(Ellipse);

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface SplashScreenProps {
  onAnimationComplete?: () => void;
}

const SplashScreen: React.FC<SplashScreenProps> = ({ onAnimationComplete }) => {
  // Animation values for logo container
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Animation values for bird/hand graphic parts
  const birdBodyAnim = useRef(new Animated.Value(0)).current;
  const birdWingAnim = useRef(new Animated.Value(0)).current;
  const birdHeadAnim = useRef(new Animated.Value(0)).current;
  const birdStrokeAnim = useRef(new Animated.Value(0)).current;

  // Animation values for text
  const textKAnim = useRef(new Animated.Value(0)).current;
  const textAAnim = useRef(new Animated.Value(0)).current;
  const textLAnim = useRef(new Animated.Value(0)).current;
  const textIAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const startAnimation = (): void => {
      // Ultra smooth building animation - all parts animate together with smooth overlaps
      Animated.parallel([
        // Container fade in
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        // Bird body - starts immediately with very smooth timing
        Animated.timing(birdBodyAnim, {
          toValue: 1,
          duration: 1200,
          delay: 200,
          useNativeDriver: true,
        }),
        // Wing - starts shortly after body with smooth overlap
        Animated.timing(birdWingAnim, {
          toValue: 1,
          duration: 1000,
          delay: 400,
          useNativeDriver: true,
        }),
        // Head - smooth overlap with wing
        Animated.timing(birdHeadAnim, {
          toValue: 1,
          duration: 800,
          delay: 600,
          useNativeDriver: true,
        }),
        // Stroke - subtle highlight effect
        Animated.timing(birdStrokeAnim, {
          toValue: 1,
          duration: 600,
          delay: 800,
          useNativeDriver: true,
        }),
        // Text K - starts as bird is completing
        Animated.timing(textKAnim, {
          toValue: 1,
          duration: 600,
          delay: 1000,
          useNativeDriver: true,
        }),
        // Text A - smooth follow
        Animated.timing(textAAnim, {
          toValue: 1,
          duration: 600,
          delay: 1100,
          useNativeDriver: true,
        }),
        // Text L - smooth follow
        Animated.timing(textLAnim, {
          toValue: 1,
          duration: 600,
          delay: 1200,
          useNativeDriver: true,
        }),
        // Text I - smooth follow
        Animated.timing(textIAnim, {
          toValue: 1,
          duration: 600,
          delay: 1300,
          useNativeDriver: true,
        }),
      ]).start(() => {
        // Hold for a moment then complete
        setTimeout(() => {
          onAnimationComplete?.();
        }, 400);
      });
    };

    // Start animation after mount
    const timer = setTimeout(startAnimation, 200);
    return () => clearTimeout(timer);
  }, [
    fadeAnim,
    birdBodyAnim,
    birdWingAnim,
    birdHeadAnim,
    birdStrokeAnim,
    textKAnim,
    textAAnim,
    textLAnim,
    textIAnim,
    onAnimationComplete,
  ]);

  // Calculate responsive size - much larger for better visibility
  const logoSize = Math.min(screenWidth * 0.95, screenHeight * 0.8, 600);

  return (
    <View style={styles.container}>
      <StatusBar
        style="dark" // dark-content
        translucent
        backgroundColor="transparent"
      />
      <Animated.View
        style={[
          styles.logoContainer,
          {
            opacity: fadeAnim,
          },
        ]}
      >
        <Svg width={logoSize} height={logoSize} viewBox="0 0 500 500">
          {/* Background */}
          <Rect x="0" y="0" width="500" height="500" fill="#FFFFFF" />

          <G>
            {/* Text "kali" with individual letter animations */}
            <G>
              {/* Letter K */}
              <AnimatedPath
                d="M248.96,285.43l-25.3-32.74v32.74h-17.25v-71.78h17.25v31.3l24.79-31.3h19.73l-27.58,35.01,27.99,36.77h-19.62Z"
                fill="#1db960"
                opacity={textKAnim}
              />
              {/* Letter A */}
              <AnimatedPath
                d="M323.12,230.99v54.43h-15.91v-4.96c-3.82,4.03-8.99,6.3-15.7,6.3-15.8,0-27.16-12.19-27.16-28.51s11.36-27.47,27.16-27.47c6.71,0,11.88,2.06,15.7,5.89v-5.68h15.91ZM306.6,258.26c0-7.95-5.06-13.43-12.6-13.43s-12.5,5.68-12.5,13.43,5.16,13.32,12.5,13.32,12.6-5.47,12.6-13.32Z"
                fill="#1db960"
                opacity={textAAnim}
              />
              {/* Letter L */}
              <AnimatedPath
                d="M332.94,213.64h17.25v71.78h-17.25v-71.78Z"
                fill="#1db960"
                opacity={textLAnim}
              />
              {/* Letter I */}
              <AnimatedPath
                d="M357.93,223.87c0-6.2,4.75-10.64,10.84-10.64s10.84,4.44,10.84,10.64-4.75,10.53-10.84,10.53-10.84-4.34-10.84-10.53ZM360.1,241.74c.93.72,4.05,2.94,8.62,2.94,1.18,0,5-.15,8.62-2.94v43.69h-17.25v-43.69Z"
                fill="#1db960"
                opacity={textIAnim}
              />
            </G>

            {/* Bird/Hand graphic with building animation */}
            <G>
              {/* Bird body - appears first */}
              <AnimatedPath
                d="M125.44,282.75s-9.27-5.27-2.72-22.38c.1-.27.26-.54.26-.54,0,0,.32-.66.6-1.19,1.32-2.56,6.07-6.98,6.07-6.98.54-.5.83-.73,1.32-1.2,4.72-4.57,29.05-28.06,34.44-31.31.16-.1.32-.21.46-.33,1.35-1.13,8.13-6.43,13.92-4.65.86.26,1.54.93,1.78,1.79.5,1.75.32,5.29-4.39,11.6-.28.38-.57.76-.81,1.16-1.73,2.86-37.17,37.28-40.24,39.56,0,0-10.28,9.74-10.69,14.48Z"
                fill="#1db960"
                stroke="#1db960"
                strokeWidth="2"
                strokeOpacity={birdStrokeAnim}
                strokeMiterlimit={10}
                opacity={birdBodyAnim}
              />
              {/* Bird wing - appears second */}
              <AnimatedPath
                d="M145.49,271.08c-.55-.57-1.98-2.61,1.19-6.04.09-.1.19-.2.75-.75l6.79-6.15c.28-.21,1.22-.61,2.21-.69,1.27-.09,2.91.11,3.86,1.47l20.37,22.67s2.09,2.03-.51,2.67c-.33.08-.67.1-1.01.1l-17.86-.39c-.29,0-.57-.04-.85-.1-1.8-.41-7.83-2.45-14.55-12.33-.11-.16-.24-.32-.38-.46Z"
                fill="#1db960"
                stroke="#1db960"
                strokeWidth="2"
                strokeOpacity={birdStrokeAnim}
                strokeMiterlimit={10}
                opacity={birdWingAnim}
              />
              {/* Bird head - appears last */}
              <AnimatedEllipse
                cx="133.14"
                cy="225.73"
                rx="12.23"
                ry="10.56"
                transform="translate(-122.68 282.59) rotate(-72)"
                fill="#1db960"
                stroke="#1db960"
                strokeWidth="2"
                strokeOpacity={birdStrokeAnim}
                strokeMiterlimit={10}
                opacity={birdHeadAnim}
              />
            </G>
          </G>
        </Svg>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default SplashScreen;
