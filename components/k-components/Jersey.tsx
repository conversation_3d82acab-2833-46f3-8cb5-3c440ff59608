import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import clsx from 'clsx';
import { getReadableTextColor } from '@/utils';

interface JerseyProps {
  color?: string;
  name?: string;
  number?: string | number;
  width?: number;
  height?: number;
  className?: string;
}

const Jersey: React.FC<JerseyProps> = ({
  color = '#1DB960',
  name = '',
  number = '',
  width = 120,
  height = 120,
  className = '',
}) => {
  return (
    <View
      className={clsx('relative', className)}
      style={[styles.container, { width, height }]}
    >
      <View
        style={[
          StyleSheet.absoluteFill,
          {
            backgroundColor: color,
            borderRadius: 8,
          },
        ]}
      />

      <Image
        source={require('@/assets/Icons/pngs/jersey.png')}
        style={{ width: '100%', height: '100%', resizeMode: 'contain' }}
      />

      {/* Jersey name */}
      {name ? (
        <Text
          style={{
            position: 'absolute',
            top: height * 0.15,
            alignSelf: 'center',
            fontSize: Math.min(width * 0.12, 14),
            color: getReadableTextColor(color),
            fontWeight: '600',
            fontFamily: 'Urbanist_800ExtraBold',
          }}
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {name.toUpperCase()}
        </Text>
      ) : null}

      {/* Jersey number */}
      {number ? (
        <Text
          style={{
            position: 'absolute',
            top: height * 0.4,
            alignSelf: 'center',
            fontSize: Math.min(width * 0.25, 30),
            color: getReadableTextColor(color),
            fontFamily: 'Urbanist_800ExtraBold',
          }}
        >
          {number}
        </Text>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default Jersey;
