import React, { useState } from 'react';
import { View, Pressable } from 'react-native';
import { Icon } from '@/components/ui/icon';
import { InstagramIcon, FacebookIcon, GlobeIcon } from '@/components/ui/icon';
import SocialLinkEditor, { SocialPlatform } from './SocialLinkEditor';
import { type TeamSocialLinks } from '@/types/teams';

interface SocialLinksSectionProps {
  socialLinks: TeamSocialLinks;
  onSave: (platform: SocialPlatform, url: string) => void;
}

const SocialLinksSection: React.FC<SocialLinksSectionProps> = ({
  socialLinks,
  onSave,
}) => {
  const [editorState, setEditorState] = useState<{
    isOpen: boolean;
    platform: SocialPlatform | null;
    initialValue: string;
  }>({
    isOpen: false,
    platform: null,
    initialValue: '',
  });
  const socialPlatforms = [
    {
      platform: 'website' as SocialPlatform,
      icon: GlobeIcon,
      value: socialLinks.website,
    },
    {
      platform: 'instagram' as SocialPlatform,
      icon: InstagramIcon,
      value: socialLinks.instagram_url,
    },
    {
      platform: 'facebook' as SocialPlatform,
      icon: FacebookIcon,
      value: socialLinks.facebook_url,
    },
  ];

  const handleIconPress = (platform: SocialPlatform, currentValue?: string) => {
    setEditorState({
      isOpen: true,
      platform,
      initialValue: currentValue || '',
    });
  };

  const handleEditorClose = () => {
    setEditorState({
      isOpen: false,
      platform: null,
      initialValue: '',
    });
  };

  const handleEditorSave = (url: string) => {
    if (editorState.platform) {
      onSave(editorState.platform, url);
    }
  };

  return (
    <View className="px-4 py-6">
      <View className="flex-row justify-center items-center gap-8">
        {socialPlatforms.map(({ platform, icon: IconComponent, value }) => (
          <Pressable
            key={platform}
            onPress={() => handleIconPress(platform, value)}
            className="w-12 h-12 rounded-full bg-background-100 border border-background-200 justify-center items-center active:bg-background-200"
          >
            <Icon
              as={IconComponent}
              size="lg"
              className={value ? 'text-primary-600' : 'text-typography-400'}
            />
          </Pressable>
        ))}
      </View>

      {editorState.platform && (
        <SocialLinkEditor
          isOpen={editorState.isOpen}
          onClose={handleEditorClose}
          platform={editorState.platform}
          initialValue={editorState.initialValue}
          onSave={handleEditorSave}
        />
      )}
    </View>
  );
};

export default SocialLinksSection;
