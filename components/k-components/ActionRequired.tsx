import React from 'react';
import { View } from 'react-native';
import { AlertTriangle } from 'lucide-react-native';
import {
  Accordion,
  AccordionItem,
  AccordionHeader,
  AccordionTrigger,
  AccordionIcon,
  AccordionContent,
} from '@/components/ui/accordion';
import { ChevronUpIcon, ChevronDownIcon } from '@/components/ui/icon';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';

interface ActionItem {
  id: string;
  label: string;
  description: string;
  condition: boolean;
  onPress: () => void;
}

export default function ActionRequired({ actions }: { actions: ActionItem[] }) {
  const filteredActions = actions.filter((action) => action.condition);

  if (filteredActions.length === 0) return null;

  return (
    <View className="bg-orange-50 border border-orange-300 rounded-lg p-4 mt-5 shadow-sm">
      <View className="flex-row items-center mb-3">
        <AlertTriangle size={19} color="#D97706" />
        <Text className="text-[#D97706] font-urbanistBold text-lg ml-2">
          Action Required
        </Text>
      </View>

      <Accordion
        type="multiple"
        variant="unfilled"
        className="border border-outline-300 rounded-md overflow-hidden"
      >
        {filteredActions.map((action) => (
          <AccordionItem key={action.id} value={action.id} className="bg-white">
            <AccordionHeader>
              <AccordionTrigger>
                {({ isExpanded }) => (
                  <>
                    <Text className="font-urbanistBold">{action.label}</Text>
                    <AccordionIcon
                      as={isExpanded ? ChevronUpIcon : ChevronDownIcon}
                      className="ml-3"
                    />
                  </>
                )}
              </AccordionTrigger>
            </AccordionHeader>
            <AccordionContent className="bg-background-40">
              <Text className="mb-3 text-typography-600 font-urbanistMedium text-sm">
                {action.description}
              </Text>
              <Button onPress={action.onPress} size="sm" variant="outline">
                <ButtonText className="font-urbanistSemiBold">
                  Take Action
                </ButtonText>
              </Button>
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </View>
  );
}
