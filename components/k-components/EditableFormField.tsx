import React, { useState, useCallback, useMemo } from 'react';
import { View } from 'react-native';
import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import FormField, { type FieldConfig } from './FormField';
import WarningDialog from './WarningDialog';

export type EditableType =
  | {
      type: 'editable';
    }
  | {
      type: 'disabled';
      message: string;
    }
  | {
      type: 'warning';
      message: string;
    };

export enum ScreenMode {
  CREATE = 'create',
  EDIT = 'edit',
}

interface EditableFieldConfig extends FieldConfig {
  editable?: EditableType;
}

interface EditableFormFieldProps {
  field: EditableFieldConfig;
  screenMode: ScreenMode;
  value: any;
  error?: string;
  onChange: (key: string, value: any) => void;
  className?: string;
}

function useEditableField(field: EditableFieldConfig, screenMode: ScreenMode) {
  const [canEdit, setCanEdit] = useState(field.editable?.type !== 'warning');

  const shouldBlockEdit = useMemo(
    () =>
      screenMode === ScreenMode.EDIT &&
      field.editable?.type === 'warning' &&
      !canEdit,
    [screenMode, field.editable?.type, canEdit]
  );

  const isEditable = useMemo(
    () =>
      screenMode === ScreenMode.CREATE ||
      field.editable?.type === 'editable' ||
      field.editable?.type === undefined ||
      canEdit,
    [screenMode, field.editable?.type, canEdit]
  );

  return {
    canEdit: isEditable,
    shouldBlockEdit,
    setCanEdit,
    editableMeta: field.editable,
  };
}

const EditableFormField: React.FC<EditableFormFieldProps> = ({
  field,
  screenMode,
  value,
  error = '',
  onChange,
  className = '',
}) => {
  const [showWarning, setShowWarning] = useState(false);
  const { canEdit, shouldBlockEdit, setCanEdit, editableMeta } =
    useEditableField(field, screenMode);

  const handleConfirm = useCallback(() => {
    setCanEdit(true);
    setShowWarning(false);
    if (value == null || value === undefined) {
      onChange(field.key, '');
    }
  }, [field.key, value, onChange, setCanEdit]);

  const handleWarningClose = useCallback(() => {
    setShowWarning(false);
  }, []);

  const handleEditPress = useCallback(() => {
    setShowWarning(true);
  }, []);

  const warningMessage = useMemo(() => {
    if (editableMeta?.type === 'warning' || editableMeta?.type === 'disabled') {
      return editableMeta.message;
    }
    return 'Changing this field may affect related data. Are you sure you want to continue?';
  }, [editableMeta]);

  return (
    <View className={className}>
      <FormField
        keyName={field.key}
        type={field.type}
        field={field}
        value={value ?? ''}
        error={error}
        onChange={onChange}
        disabled={!canEdit}
      />

      {shouldBlockEdit && editableMeta?.type === 'warning' && (
        <Button
          variant="link"
          size="xs"
          onPress={handleEditPress}
          className="self-center"
        >
          <Text className="font-urbanistSemiBold text-xs border-b border-typography-600 text-typography-600">
            Edit This Field
          </Text>
        </Button>
      )}

      {/* Disabled message */}
      {editableMeta?.type === 'disabled' && (
        <Text className="text-xs text-yellow-600 font-urbanist mt-1">
          {editableMeta.message}
        </Text>
      )}

      {/* Warning Dialog */}
      <WarningDialog
        open={showWarning}
        message={warningMessage}
        onClose={handleWarningClose}
        onConfirm={handleConfirm}
      />
    </View>
  );
};

export default EditableFormField;
export type { EditableFieldConfig };
