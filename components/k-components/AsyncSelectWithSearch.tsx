import React, { useState, useCallback, useMemo } from 'react';
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicatorWrapper,
  ActionsheetDragIndicator,
  ActionsheetItem,
  ActionsheetItemText,
  ActionsheetFlatList,
} from '@/components/ui/actionsheet';
import {
  Checkbox,
  CheckboxIndicator,
  CheckboxIcon,
} from '@/components/ui/checkbox';
import { CheckIcon } from '@/components/ui/icon';
import { Spinner } from '@/components/ui/spinner';
import { VStack } from '@/components/ui/vstack';
import { Button, ButtonText } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import SearchBar from './SearchBar';
import { Pressable, View } from 'react-native';
import NoDataFound from './NoDataFound';
import { Divider } from '../ui/divider';

export interface Option {
  label: string;
  value: string;
  [key: string]: any;
}

interface Props {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (selected: Option[] | Option | null) => void;
  selectedValues?: Option[];
  selectedValue?: Option;
  multiple?: boolean;
  maxSelect?: number;
  options: Option[];
  hasMore: boolean;
  loadMore: (searchQuery: string, page: number) => Promise<void>;
  placeholder?: string;
  noDataFoundText?: string;
  renderOption?: (option: Option, isSelected: boolean) => React.ReactNode;
  onSearchChange?: (query: string) => void;
}

export const AsyncSelectWithSearch = ({
  isOpen,
  onClose,
  onSelect,
  selectedValues = [],
  selectedValue,
  multiple = false,
  maxSelect,
  options,
  hasMore,
  loadMore,
  placeholder = 'Search...',
  noDataFoundText = 'No results',
  renderOption,
  onSearchChange,
}: Props) => {
  const [page, setPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [internalSelected, setInternalSelected] = useState<Option[]>([]);

  React.useEffect(() => {
    if (isOpen) {
      if (multiple) {
        setInternalSelected(selectedValues);
      } else {
        setInternalSelected(selectedValue ? [selectedValue] : []);
      }
    }
  }, [isOpen]);

  const localSelected = useMemo(() => {
    return internalSelected;
  }, [internalSelected]);

  const handleSearchChange = useCallback(
    (query: string) => {
      setSearchQuery(query);
      setPage(1);
      onSearchChange?.(query);
    },
    [onSearchChange]
  );

  const toggleSelect = useCallback(
    (option: Option) => {
      if (!multiple) {
        onSelect(option);
        onClose();
        return;
      }

      const exists = internalSelected.some((o) => o.value === option.value);
      const updated = exists
        ? internalSelected.filter((o) => o.value !== option.value)
        : [...internalSelected, option];

      if (maxSelect === undefined || updated.length <= maxSelect) {
        setInternalSelected(updated);
      }
    },
    [
      multiple,
      internalSelected,
      maxSelect,
      onSelect,
      onClose,
      setInternalSelected,
    ]
  );

  const renderItem = useCallback(
    ({ item }: { item: Option }) => {
      const isSelected = localSelected.some((o) => o.value === item.value);
      const isDisabled =
        multiple &&
        !isSelected &&
        maxSelect !== undefined &&
        localSelected.length >= maxSelect;

      return (
        <ActionsheetItem
          key={item.value}
          onPress={() => toggleSelect(item)}
          isDisabled={isDisabled}
          className="rounded-lg active:bg-none"
        >
          <View className="flex-row items-center gap-4">
            {multiple && (
              <Checkbox
                onChange={() => toggleSelect(item)}
                size="md"
                value={item.value}
                isChecked={isSelected}
              >
                <CheckboxIndicator>
                  <CheckboxIcon as={CheckIcon} />
                </CheckboxIndicator>
              </Checkbox>
            )}
            {renderOption ? (
              renderOption(item, isSelected)
            ) : (
              <View className="flex-1">
                <ActionsheetItemText
                  className={` text-base py-2 ${
                    isSelected ? 'font-urbanistBold' : 'font-urbanistMedium'
                  }`}
                >
                  {item.label}
                </ActionsheetItemText>
              </View>
            )}
          </View>
        </ActionsheetItem>
      );
    },
    [localSelected, multiple, maxSelect, toggleSelect, renderOption]
  );

  const handleEndReached = useCallback(async () => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      setLoading(true);
      try {
        await loadMore(searchQuery, nextPage);
        setPage(nextPage);
      } finally {
        setLoading(false);
      }
    }
  }, [loading, hasMore, page, searchQuery, loadMore]);

  const keyExtractor = useCallback((item: any) => item.value, []);

  const handleModalClose = useCallback(() => {
    setSearchQuery('');
    setInternalSelected([]);
    setPage(1);
    onClose();
  }, [onClose]);

  const handleContinue = useCallback(() => {
    if (multiple) {
      onSelect(internalSelected);
    }
    setSearchQuery('');
    setInternalSelected([]);
    setPage(1);
    onClose();
  }, [multiple, internalSelected, onSelect, onClose]);

  const handleCancel = useCallback(() => {
    setSearchQuery('');
    setInternalSelected([]);
    setPage(1);
    onClose();
  }, [onClose]);

  return (
    <Actionsheet isOpen={isOpen} onClose={handleModalClose} snapPoints={[80]}>
      <ActionsheetBackdrop />
      <ActionsheetContent>
        <ActionsheetDragIndicatorWrapper>
          <ActionsheetDragIndicator />
        </ActionsheetDragIndicatorWrapper>

        <VStack space="sm" className="w-full mt-4 flex-1">
          <SearchBar
            value={searchQuery}
            onDebouncedChange={handleSearchChange}
            placeholder={placeholder}
          />

          <ActionsheetFlatList
            data={options}
            renderItem={renderItem as any}
            showsVerticalScrollIndicator={false}
            keyExtractor={keyExtractor}
            onEndReached={handleEndReached}
            onEndReachedThreshold={0.4}
            ListFooterComponent={loading ? <Spinner className="my-2" /> : null}
            className="flex-1"
            contentContainerClassName="pb-6"
            ListEmptyComponent={<NoDataFound title={noDataFoundText} />}
            style={{ flexGrow: 1 }}
            ItemSeparatorComponent={() => <Divider />}
          />

          {multiple && (
            <View className="py-4 border-t border-outline-100 bg-white">
              <VStack className="space-y-3 gap-2">
                <Button
                  className="bg-primary-0"
                  onPress={handleContinue}
                  isDisabled={internalSelected.length === 0}
                >
                  <ButtonText className="font-urbanistSemiBold">
                    Continue ({internalSelected.length})
                  </ButtonText>
                </Button>

                <Pressable onPress={handleCancel}>
                  <Text className="text-center text-typography-600 font-urbanistMedium">
                    Cancel
                  </Text>
                </Pressable>
              </VStack>
            </View>
          )}
        </VStack>
      </ActionsheetContent>
    </Actionsheet>
  );
};
