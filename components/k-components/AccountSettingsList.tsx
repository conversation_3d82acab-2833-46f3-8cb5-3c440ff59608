import React from 'react';
import { View, FlatList } from 'react-native';
import { useRouter } from 'expo-router';
import { useAuthUser } from '@/hooks/useAuthUser';
import { triggerHapticFeedback } from '@/utils';
import SettingsItem from '@/components/k-components/SettingsListItem';
import {
  nonLoggedinSettingsItems,
  loggedInSettingsItems,
} from '@/utils/account-utils/account-settings-utils';

interface AccountSettingsListProps {}

export function AccountSettingsList({}: AccountSettingsListProps) {
  const { user } = useAuthUser();
  const router = useRouter();

  const settingsToRender = user
    ? loggedInSettingsItems
    : nonLoggedinSettingsItems;

  const navigateToSetting = (path: string) => {
    if (path) {
      router.push(path as any);
      triggerHapticFeedback();
    }
  };

  const renderSeparator = () => (
    <View className="border-b border-gray-200 my-3" />
  );

  const renderSettingsItem = ({ item }: { item: typeof settingsToRender[0] }) => (
    <SettingsItem
      title={item.title}
      icon={item.icon}
      onPress={() => navigateToSetting(item.path)}
    />
  );

  return (
    <View className="flex">
      <FlatList
        data={settingsToRender}
        keyExtractor={(item) => item.title}
        ItemSeparatorComponent={renderSeparator}
        renderItem={renderSettingsItem}
      />
    </View>
  );
}
