import React from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';

const NoDataFound = ({
  title,
  subtitle,
  action,
  className,
}: {
  title: string;
  subtitle?: string;
  action?: React.ReactNode;
  className?: string;
}) => (
  <View
    className={`flex flex-col items-center justify-center min-h-96 px-4 ${className}`}
  >
    <Text className="text-4xl font-urbanistExtraBold text-typography-300 text-center">
      {title}
    </Text>
    {subtitle && (
      <Text className="text-sm font-urbanistMedium text-typography-500 mt-1 mb-6 text-center">
        {subtitle}
      </Text>
    )}

    {action ? <View className="mt-2">{action}</View> : null}
  </View>
);

export default NoDataFound;
