import React from 'react';
import { View, Text, Pressable } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { VStack } from '@/components/ui/vstack';
import { CTAButton } from '@/components/ui/primaryCTAbutton';
import { triggerHapticFeedback } from '@/utils';

interface StickyBottomButtonsProps {
  primaryButton: {
    title: string;
    onPress: () => void;
    loading?: boolean;
    disabled?: boolean;
  };
  secondaryButton?: {
    title: string;
    onPress: () => void;
    variant?: 'cancel' | 'button';
  };
  className?: string;
}

export const StickyBottomButtons: React.FC<StickyBottomButtonsProps> = ({
  primaryButton,
  secondaryButton,
  className = '',
}) => {
  const insets = useSafeAreaInsets();

  return (
    <View
      className={`absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2 ${className}`}
      style={{ paddingBottom: Math.max(insets.bottom, 16) }}
    >
      <VStack space="sm">
        <CTAButton
          title={primaryButton.title}
          onPress={primaryButton.onPress}
          loading={primaryButton.loading}
          isFormValid={!primaryButton.disabled}
        />
        {secondaryButton && (
          <>
            {secondaryButton.variant === 'cancel' ? (
              <Pressable
                onPress={() => {
                  triggerHapticFeedback();
                  secondaryButton.onPress();
                }}
              >
                <Text className="text-center text-typography-600 font-urbanistMedium pb-1">
                  {secondaryButton.title}
                </Text>
              </Pressable>
            ) : (
              <CTAButton
                title={secondaryButton.title}
                onPress={secondaryButton.onPress}
                isFormValid={true}
              />
            )}
          </>
        )}
      </VStack>
    </View>
  );
};
