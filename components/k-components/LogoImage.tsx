import React, { memo, useState } from 'react';
import { Image } from 'expo-image';
import { View, ActivityIndicator, Text } from 'react-native';
import clsx from 'clsx';
import { getInitials } from '@/utils';

interface LogoImageProps {
  logoUrl?: string | null;
  fallbackText?: string;
  width?: number;
  height?: number;
  borderRadius?: number;
  className?: string;
  fallBacktextClassName?: string;
}

const LogoImage: React.FC<LogoImageProps> = ({
  logoUrl,
  fallbackText,
  width = 75,
  height = 75,
  borderRadius = 12,
  className = '',
  fallBacktextClassName = '',
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  const initials = getInitials(fallbackText || '') || '?';

  if (!logoUrl || error) {
    return (
      <View
        className={clsx('bg-gray-200 items-center justify-center', className)}
        style={{ width, height, borderRadius }}
      >
        <Text
          className={clsx(
            'text-2xl font-urbanistBold text-gray-700',
            fallBacktextClassName
          )}
        >
          {initials}
        </Text>
      </View>
    );
  }

  return (
    <View className={clsx('relative', className)} style={{ width, height }}>
      {loading && (
        <View
          className="absolute inset-0 items-center justify-center bg-gray-100 rounded-xl"
          style={{ borderRadius }}
        >
          <ActivityIndicator size="small" color="#1DB960" />
        </View>
      )}

      <Image
        source={logoUrl}
        style={{ width: '100%', height: '100%', borderRadius }}
        contentFit="cover"
        cachePolicy="memory-disk"
        onLoadStart={() => {
          setLoading(true);
          setError(false);
        }}
        onLoadEnd={() => setLoading(false)}
        onError={() => {
          setLoading(false);
          setError(true);
        }}
      />
    </View>
  );
};

export default memo(LogoImage);
