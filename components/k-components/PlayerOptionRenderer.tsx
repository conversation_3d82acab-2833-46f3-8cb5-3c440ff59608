import React from 'react';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { type Option } from './AsyncSelectWithSearch';

interface PlayerOptionRendererProps {
  option: Option;
  isSelected: boolean;
  showJerseyNumber?: boolean;
  jerseyNumberPrefix?: string;
}

export const PlayerOptionRenderer: React.FC<PlayerOptionRendererProps> = ({
  option,
  isSelected,
  showJerseyNumber = true,
}) => {
  return (
    <VStack className="flex-1 py-2">
      <Text
        numberOfLines={1}
        ellipsizeMode="tail"
        className={`text-base ${
          isSelected ? 'font-urbanistBold' : 'font-urbanistMedium'
        }`}
      >
        {option.label}
      </Text>
      {showJerseyNumber && option.player?.jersey_number && (
        <Text
          className={`text-xs text-typography-600 ${
            isSelected ? 'font-urbanistMedium' : 'font-urbanist'
          }`}
        >
          Jersey #{option.player.jersey_number}
        </Text>
      )}
    </VStack>
  );
};
