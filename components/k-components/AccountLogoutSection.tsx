import React from 'react';
import { View } from 'react-native';
import { Button, ButtonText } from '@/components/ui/button';
import { useRouter } from 'expo-router';
import { useAuthUser } from '@/hooks/useAuthUser';
import { useLogout } from '@/hooks/useLogout';
import { triggerHapticFeedback } from '@/utils';
import { LogOutIcon } from 'lucide-react-native';
import SCREENS from '@/constants/Screens';
import SettingsItem from '@/components/k-components/SettingsListItem';

interface AccountLogoutSectionProps {}

export function AccountLogoutSection({}: AccountLogoutSectionProps) {
  const { user } = useAuthUser();
  const router = useRouter();
  const { logout } = useLogout();

  const handleLogout = () => {
    triggerHapticFeedback();
    logout();
  };

  const handleLoginNavigation = () => {
    router.navigate(SCREENS.LOGIN);
  };

  if (!user) {
    return (
      <Button
        className="bg-primary-0 mt-24 rounded-full"
        size="lg"
        variant="solid"
        onPress={handleLoginNavigation}
      >
        <ButtonText className="font-urbanistSemiBold">
          Login / Sign Up
        </ButtonText>
      </Button>
    );
  }

  return (
    <>
      <View className="border-b border-gray-200 my-3" />
      <SettingsItem
        title="Logout"
        icon={LogOutIcon}
        showCevron={false}
        iconClassName="text-primary-0"
        textClassName="text-primary-0"
        onPress={handleLogout}
      />
    </>
  );
}
