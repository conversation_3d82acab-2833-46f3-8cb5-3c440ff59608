import React from 'react';
import { Pressable, View } from 'react-native';
import { Text } from '@/components/ui/text';
import { Icon as IconComponent } from '@/components/ui/icon';
import { ChevronRightIcon } from 'lucide-react-native';
import clsx from 'clsx';

const SettingsItem = ({
  title,
  icon,
  onPress,
  showCevron = true,
  textClassName,
  iconClassName,
}: {
  title: string;
  icon: React.ComponentType<any>;
  onPress?: () => void;
  showCevron?: boolean;
  textClassName?: string;
  iconClassName?: string;
}) => (
  <Pressable
    className="flex-row items-center justify-between py-3"
    onPress={onPress}
  >
    <View className="flex-row gap-5 items-center">
      <IconComponent
        size="xl"
        className={clsx('text-typography-900', iconClassName)}
        as={icon}
      />
      <Text
        className={clsx(
          'text-lg font-urbanistSemiBold text-typography-900',
          textClassName
        )}
      >
        {title}
      </Text>
    </View>
    {showCevron && (
      <IconComponent
        size="xl"
        className="text-typography-400"
        as={ChevronRightIcon}
      />
    )}
  </Pressable>
);

export default SettingsItem;
