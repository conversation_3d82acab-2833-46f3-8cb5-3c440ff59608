import React from 'react';
import { View, Image } from 'react-native';
import { Text } from '@/components/ui/text';
import { useAuthUser } from '@/hooks/useAuthUser';
import { Avatar, AvatarFallbackText } from '@/components/ui/avatar';
import { GradientHeader } from '@/components/k-components/GradientHeader';
import { ProfileIcon } from '@/assets/Icons/ProfileIcon';
import { useFocusEffect } from 'expo-router';
import { useCallback } from 'react';

interface AccountHeaderProps {}

export function AccountHeader({}: AccountHeaderProps) {
  const { user, refreshUser } = useAuthUser();

  // Refresh user data when the screen comes into focus
  useFocusEffect(
    useCallback(() => {
      refreshUser();
    }, [refreshUser])
  );

  if (!user) {
    return (
      <GradientHeader className="flex flex-row items-center gap-5 min-h-[180px] rounded-b-[3rem]">
        <ProfileIcon size={90} />
        <View className="flex flex-col w-2/3 gap-2">
          <Text className="text-3xl font-urbanistSemiBold text-white">
            Account
          </Text>
          <Text className="text-md font-urbanist text-white">
            Login or Sign up to manage your account
          </Text>
        </View>
      </GradientHeader>
    );
  }

  const getUserInitial = () => {
    return (
      user?.user_metadata.full_name?.[0]?.toUpperCase() ||
      user?.email?.[0]?.toUpperCase() ||
      'U'
    );
  };

  const getUserDisplayName = () => {
    return user.user_metadata.full_name || 'Stranger';
  };

  return (
    <GradientHeader className="flex flex-row items-center gap-5 min-h-[180px] rounded-b-[3rem]">
      <Avatar size="2xl" className="flex items-center justify-center">
        {user.user_metadata.avatar_url ? (
          <Image source={{ uri: user.user_metadata.avatar_url }} />
        ) : (
          <AvatarFallbackText className="pt-3 text-white">
            {getUserInitial()}
          </AvatarFallbackText>
        )}
      </Avatar>

      <View className="flex flex-col">
        <Text className="text-2xl font-urbanistSemiBold text-white">
          {getUserDisplayName()}
        </Text>
        <Text className="text-sm font-urbanist text-white mt-1">
          {user.email}
        </Text>
      </View>
    </GradientHeader>
  );
}
