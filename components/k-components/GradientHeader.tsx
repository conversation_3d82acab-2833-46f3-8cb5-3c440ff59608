import { ReactNode } from 'react';
import { LinearGradient } from 'expo-linear-gradient';
import { ImageBackground, StyleSheet, StatusBar, Platform } from 'react-native';
import clsx from 'clsx';

interface GradientHeaderProps {
  children?: ReactNode;
  className?: string;
  showBGImage?: boolean;
  styles?: any;
}

export const GradientHeader = ({
  children,
  className,
  showBGImage = true,
  styles,
}: GradientHeaderProps) => {
  const statusBarHeight =
    Platform.OS === 'android' ? StatusBar.currentHeight || 0 : 0;
  const paddingTop = 25 + statusBarHeight;

  return (
    <LinearGradient
      colors={['#196339', '#1DB960']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
      className={clsx('relative overflow-hidden px-8 pb-8', className)}
      style={[styles, { paddingTop }]}
    >
      {showBGImage && (
        <ImageBackground
          source={require('../../assets/images/header-bg.png')}
          resizeMode="cover"
          style={{
            width: 220,
            height: 220,
            position: 'absolute',
            top: 0,
            right: 0,
          }}
        />
      )}

      {children && children}
    </LinearGradient>
  );
};
