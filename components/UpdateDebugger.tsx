import React, { useState, useEffect } from 'react';
import { Alert } from 'react-native';
import * as Updates from 'expo-updates';
import { Box } from '@/components/ui/box';
import { Button, ButtonText } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Badge, BadgeText } from '@/components/ui/badge';
import { getCurrentUpdateInfo } from '@/lib/updates';

interface UpdateDebuggerProps {
  /**
   * Show this component only in development or always
   */
  showInProduction?: boolean;
}

export function UpdateDebugger({
  showInProduction = false,
}: UpdateDebuggerProps) {
  const [updateInfo, setUpdateInfo] = useState<any>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [lastChecked, setLastChecked] = useState<Date | null>(null);
  const [error, setError] = useState<string | null>(null);

  const currentInfo = getCurrentUpdateInfo();

  // Don't show in production unless explicitly enabled
  if (!__DEV__ && !showInProduction) {
    return null;
  }

  const checkForUpdates = async () => {
    setIsChecking(true);
    setError(null);

    try {
      const update = await Updates.checkForUpdateAsync();
      setUpdateInfo(update);
      setLastChecked(new Date());

      if (update.isAvailable) {
        Alert.alert(
          '🎉 Update Available!',
          `New update found!\nManifest: ${update.manifest?.id?.substring(
            0,
            8
          )}...`,
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert('✅ No Updates', 'You have the latest version');
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMsg);
      Alert.alert('❌ Error', errorMsg);
    } finally {
      setIsChecking(false);
    }
  };

  const downloadUpdate = async () => {
    if (!updateInfo?.isAvailable) return;

    try {
      Alert.alert('📥 Downloading...', 'Update is being downloaded');
      const result = await Updates.fetchUpdateAsync();

      if (result.isNew) {
        Alert.alert(
          '✅ Download Complete!',
          'Update downloaded. Restart app to apply?',
          [
            { text: 'Later', style: 'cancel' },
            { text: 'Restart Now', onPress: () => Updates.reloadAsync() },
          ]
        );
      }
    } catch (err) {
      Alert.alert(
        '❌ Download Failed',
        err instanceof Error ? err.message : 'Unknown error'
      );
    }
  };

  const forceRestart = () => {
    Alert.alert(
      '🔄 Restart App',
      'This will restart the app and apply any downloaded updates',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Restart', onPress: () => Updates.reloadAsync() },
      ]
    );
  };

  return (
    <Box className="p-4 m-4 bg-gray-100 rounded-lg border border-gray-300">
      <Text className="text-lg font-bold mb-3">🔧 Update Debugger</Text>

      {/* Current Update Info */}
      <Box className="mb-4 space-y-1">
        <Text className="font-semibold">Current Update Info:</Text>
        <Text className="text-sm">
          Update ID: {currentInfo.updateId?.substring(0, 12) || 'N/A'}
        </Text>
        <Text className="text-sm">Channel: {currentInfo.channel || 'N/A'}</Text>
        <Text className="text-sm">
          Runtime Version: {currentInfo.runtimeVersion || 'N/A'}
        </Text>
        <Box className="flex-row space-x-2 mt-2">
          <Badge variant={currentInfo.isEmbeddedLaunch ? 'solid' : 'outline'}>
            <BadgeText>
              {currentInfo.isEmbeddedLaunch ? 'Embedded' : 'Updated'}
            </BadgeText>
          </Badge>
        </Box>
      </Box>

      {/* Last Check Info */}
      {lastChecked && (
        <Box className="mb-4">
          <Text className="text-sm text-gray-600">
            Last checked: {lastChecked.toLocaleTimeString()}
          </Text>
          {updateInfo && (
            <Text className="text-sm">
              Update available: {updateInfo.isAvailable ? '✅ Yes' : '❌ No'}
            </Text>
          )}
        </Box>
      )}

      {/* Error Display */}
      {error && (
        <Box className="mb-4 p-2 bg-red-100 rounded">
          <Text className="text-red-800 text-sm">{error}</Text>
        </Box>
      )}

      {/* Action Buttons */}
      <Box className="space-y-2 gap-2">
        <Button
          onPress={checkForUpdates}
          disabled={isChecking}
          className="bg-blue-600"
        >
          <ButtonText>
            {isChecking ? 'Checking...' : 'Check for Updates'}
          </ButtonText>
        </Button>

        {updateInfo?.isAvailable && (
          <Button onPress={downloadUpdate} className="bg-green-600">
            <ButtonText>Download Update</ButtonText>
          </Button>
        )}

        <Button onPress={forceRestart} variant="outline">
          <ButtonText>Force Restart</ButtonText>
        </Button>
      </Box>

      {/* Development Note */}
      {__DEV__ && (
        <Box className="mt-4 p-2 bg-yellow-100 rounded">
          <Text className="text-yellow-800 text-xs">
            📝 Note: Updates don't work in development mode. Build the app to
            test updates.
          </Text>
        </Box>
      )}
    </Box>
  );
}

export default UpdateDebugger;
