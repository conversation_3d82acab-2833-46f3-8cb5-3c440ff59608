import { View, ScrollView, StatusBar } from 'react-native';
import { useRouter } from 'expo-router';
import { Icon, ChevronLeftIcon } from '@/components/ui/icon';
import { Text } from '@/components/ui/text';
import { Button } from './ui/button';
import clsx from 'clsx';

interface NavLayoutProps {
  title: string;
  children: React.ReactNode;
  unmountOnBlur?: boolean;
  isFullscreen?: boolean;
  noScroll?: boolean;
  right?: React.ReactNode;
  className?: string;
}

export function NavLayout({
  title,
  children,
  isFullscreen = true,
  noScroll = false,
  right = null,
  className = '',
}: NavLayoutProps) {
  const router = useRouter();

  const paddingBottom = isFullscreen ? 0 : 80;

  const containerStyle = {
    paddingBottom,
    flexGrow: 1,
  };

  const headerStyle = {
    paddingHorizontal: 16,
    paddingTop: StatusBar.currentHeight ? StatusBar.currentHeight + 16 : 16,
  };

  return (
    <View style={{ flex: 1 }} className="bg-background-0">
      <View
        style={{ ...headerStyle }}
        className={clsx(
          'flex-row items-center justify-between pt-1 pb-3 px-5 bg-background-0 py-4'
        )}
      >
        <View className="flex-row items-center gap-6 w-full flex-1">
          <Button onTouchStart={() => router.back()} variant="link">
            <Icon as={ChevronLeftIcon} size="xl" />
          </Button>
          <Text
            className="text-2xl font-urbanistSemiBold flex-1"
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {title}
          </Text>
        </View>
        {right && <View>{right}</View>}
      </View>

      {noScroll ? (
        <View style={{ flex: 1, ...containerStyle }}>{children}</View>
      ) : (
        <ScrollView
          className={clsx(className)}
          contentContainerStyle={containerStyle}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {children}
        </ScrollView>
      )}
    </View>
  );
}
