import React, { useState, memo } from 'react';
import { View, TextInputProps, Pressable } from 'react-native';
import { AlertCircleIcon } from 'lucide-react-native';
import { HStack } from '../hstack';
import { Input, InputField } from '../input';
import { cn } from '@/utils';
import { Text } from '../text';

type CustomInputProps = {
  label?: string;
  value?: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  maxLength?: number;
  validate?: (value: string) => string | null;
  className?: string;
  inputFieldClassName?: string;
  inputClassName?: string;
  iconPosition?: 'left' | 'right';
  icon?: React.ReactNode;
  onIconPress?: () => void;
  type?: 'text' | 'number' | 'password'; // ✅ safe custom prop
} & Omit<TextInputProps, 'onChangeText' | 'value'>;

export const CustomInput: React.FC<CustomInputProps> = memo(
  ({
    label,
    value = '',
    onChangeText,
    placeholder,
    maxLength,
    validate,
    className = '',
    inputFieldClassName = '',
    inputClassName = '',
    icon,
    iconPosition = 'right',
    onIconPress,
    type = 'text',
    ...props
  }) => {
    const [error, setError] = useState<string | null>(null);

    const handleBlur = (ev: any) => {
      if (validate && value) {
        const validationMessage = validate(value);
        setError(validationMessage);
      }
      props?.onBlur?.(ev);
    };

    const handleFocus = (ev: any) => {
      setError(null);
      props?.onFocus?.(ev);
    };

    const keyboardType: TextInputProps['keyboardType'] =
      type === 'number' ? 'numeric' : 'default';

    const secureTextEntry = type === 'password';

    const renderIcon = () => {
      if (!icon) return null;
      return onIconPress ? (
        <Pressable onTouchStart={onIconPress} className="pr-3">
          {icon}
        </Pressable>
      ) : (
        <View className="pr-3">{icon}</View>
      );
    };

    return (
      <View className={`w-full ${className}`}>
        {label && <Text className="mb-1 font-urbanistSemiBold">{label}</Text>}
        <Input
          className={cn(
            `flex-row gap-1 items-center border ${
              error ? 'border-red-500' : 'border-gray-300'
            } rounded bg-white`,
            inputClassName
          )}
          size="lg"
        >
          {icon && iconPosition === 'left' && renderIcon()}
          <InputField
            className={cn(
              'font-urbanist text-base w-full px-3 border-none shadow-none self-center',
              inputFieldClassName
            )}
            placeholderClassName="font-urbanist text-xs"
            placeholder={placeholder}
            style={{ fontFamily: 'Urbanist_400Regular' }}
            value={value}
            onChangeText={(text) => {
              onChangeText(text);
              if (error) setError(null);
            }}
            autoCapitalize="none"
            onBlur={handleBlur}
            onFocus={handleFocus}
            maxLength={maxLength}
            keyboardType={keyboardType}
            //@ts-ignore
            type={type}
            secureTextEntry={secureTextEntry}
            selectionColor="#1db960"
            {...props} // ✅ spread remaining TextInput props
          />
          {icon && iconPosition === 'right' && renderIcon()}
        </Input>
        {error && (
          <HStack space="sm" className="mt-1 flex items-center">
            <AlertCircleIcon size={14} color="#ef4444" className="mr-1" />
            <Text className="text-red-500 text-sm font-urbanist">{error}</Text>
          </HStack>
        )}
      </View>
    );
  }
);
