import React from 'react';
import {
  ScrollView,
  View,
  StyleSheet,
  ViewStyle,
  StatusBar,
  Platform,
} from 'react-native';

interface LayoutProps {
  children: React.ReactNode;
  noScroll?: boolean;
  isFullscreen?: boolean;
  style?: ViewStyle;
}

export function Layout({
  children,
  noScroll = false,
  isFullscreen = true,
  style,
}: LayoutProps) {
  const containerStyle = [
    styles.container,
    {
      paddingBottom: isFullscreen ? 0 : 80,
      paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
    },
    style,
  ];

  return (
    <View style={styles.safe} className="bg-white">
      {noScroll ? (
        <View style={containerStyle}>{children}</View>
      ) : (
        <ScrollView
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={containerStyle}
          showsVerticalScrollIndicator={false}
        >
          {children}
        </ScrollView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  safe: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    paddingHorizontal: 16,
    paddingTop: 16,
    flexGrow: 1,
  },
});
