import {
  But<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ButtonIcon,
} from '@/components/ui/button';
import { LucideIcon } from 'lucide-react-native';
import { View } from 'react-native';
import { cn } from '@/utils';

interface CTAButtonProps {
  onPress: () => void | Promise<void>;
  loading?: boolean;
  lefticon?: LucideIcon;
  isFormValid?: boolean;
  title: string;
  size?: 'sm' | 'md' | 'lg';
}

export function CTAButton({
  onPress,
  loading = false,
  isFormValid = true,
  title,
  lefticon,
  ...props
}: CTAButtonProps) {
  return (
    <Button
      {...props}
      onTouchStart={onPress}
      isDisabled={!isFormValid}
      className={`font-urbanistBold ${
        isFormValid ? 'bg-primary-0' : 'bg-typography-300'
      }`}
    >
      {loading ? (
        <ButtonSpinner size="small" className="text-white" />
      ) : (
        <>
          {lefticon && <ButtonIcon as={lefticon} />}
          <ButtonText
            className={`font-urbanistSemiBold ${
              isFormValid ? 'text-white' : 'text-typography-500'
            }`}
          >
            {title}
          </ButtonText>
        </>
      )}
    </Button>
  );
}
