import React from 'react';
import { Text, TouchableOpacity, StyleSheet } from 'react-native';
import Constants from 'expo-constants';
import { useGoogleAuth } from '@/hooks/useGoogleAuth';

// Conditional import for Google Sign-In Button
let GoogleSigninButton: any = null;

const isExpoGo = Constants.executionEnvironment === 'storeClient';

if (!isExpoGo) {
  try {
    const googleSigninModule = require('@react-native-google-signin/google-signin');
    GoogleSigninButton = googleSigninModule.GoogleSigninButton;
  } catch (error) {
    console.warn('Google Sign-In button not available:', error);
  }
}

interface GoogleSignInButtonProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export function GoogleSignInButton({
  onSuccess,
  onError,
}: GoogleSignInButtonProps) {
  const { signInWithGoogle, loading } = useGoogleAuth();

  const handleGoogleSignIn = async () => {
    try {
      const success = await signInWithGoogle();
      if (success) {
        onSuccess?.();
      } else {
        onError?.('Something went wrong');
      }
    } catch (error: any) {
      onError?.(error.message || 'Something went wrong');
    }
  };

  // If Google Sign-In button is not available (Expo Go), show a fallback
  if (!GoogleSigninButton) {
    return (
      <TouchableOpacity
        style={styles.fallbackButton}
        onPress={handleGoogleSignIn}
        disabled={loading}
      >
        <Text style={styles.fallbackButtonText}>
          {loading ? 'Signing in...' : 'Continue with Google'}
        </Text>
        <Text style={styles.fallbackSubtext}>
          (Run "expo prebuild" then "expo run:android/ios")
        </Text>
      </TouchableOpacity>
    );
  }

  return (
    <GoogleSigninButton
      size={GoogleSigninButton.Size.Wide}
      color={GoogleSigninButton.Color.Dark}
      onPress={handleGoogleSignIn}
      disabled={loading}
    />
  );
}

const styles = StyleSheet.create({
  fallbackButton: {
    backgroundColor: '#4285F4',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
  },
  fallbackButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  fallbackSubtext: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    marginTop: 2,
  },
});
