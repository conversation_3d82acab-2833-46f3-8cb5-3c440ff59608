import React, { useState } from "react";
import { Alert } from "react-native";
import {
  MatchAction,
  MatchActionHandlers,
  getMatchActions,
  getActionButtonStyle,
} from "@/utils/matchActions";

export interface MatchActionsProps {
  scheduledDate: string | null;
  currentStatus: string;
  hasResults?: boolean;
  handlers: MatchActionHandlers;
  children: (
    actions: MatchAction[],
    handleActionPress: (action: MatchAction) => void,
    loadingAction: string | null
  ) => React.ReactNode;
}

const MatchActions: React.FC<MatchActionsProps> = ({
  scheduledDate,
  currentStatus,
  hasResults = false,
  handlers,
  children,
}) => {
  const [loadingAction, setLoadingAction] = useState<string | null>(null);

  // Get available actions based on match status
  const actions = getMatchActions(scheduledDate, currentStatus, hasResults);

  const handleActionPress = async (action: MatchAction) => {
    // Show confirmation dialog if required
    if (action.requiresConfirmation && action.confirmationMessage) {
      Alert.alert(
        "Confirm Action",
        action.confirmationMessage,
        [
          {
            text: "Cancel",
            style: "cancel",
          },
          {
            text: "Confirm",
            style: action.type === "danger" ? "destructive" : "default",
            onPress: () => executeAction(action),
          },
        ]
      );
    } else {
      executeAction(action);
    }
  };

  const executeAction = async (action: MatchAction) => {
    setLoadingAction(action.id);

    try {
      // Map action IDs to handler functions
      switch (action.id) {
        case "start":
          await handlers.onStartMatch?.();
          break;
        case "edit":
          await handlers.onEditMatch?.();
          break;
        case "cancel":
          await handlers.onCancelMatch?.();
          break;
        case "delete":
          await handlers.onDeleteMatch?.();
          break;
        case "reschedule":
          await handlers.onRescheduleMatch?.();
          break;
        case "mark_completed":
          await handlers.onMarkCompleted?.();
          break;
        case "view_results":
          await handlers.onViewResults?.();
          break;
        case "add_results":
          await handlers.onAddResults?.();
          break;
        case "edit_results":
          await handlers.onEditResults?.();
          break;
        case "resume":
          await handlers.onResumeMatch?.();
          break;
        case "restart":
          await handlers.onRestartMatch?.();
          break;
        case "uncancel":
          await handlers.onUncancelMatch?.();
          break;
        default:
          console.warn(`Unknown action: ${action.id}`);
      }
    } catch (error) {
      console.error(`Error executing action ${action.id}:`, error);
      Alert.alert(
        "Error",
        `Failed to ${action.title.toLowerCase()}. Please try again.`
      );
    } finally {
      setLoadingAction(null);
    }
  };

  // Return the render prop with actions and handlers
  return children(actions, handleActionPress, loadingAction);
};

export default MatchActions;
