import React, { useState } from 'react';
import { Alert } from 'react-native';
import { VStack } from '@/components/ui/vstack';
import { CTAButton } from '@/components/ui/primaryCTAbutton';
import { 
  MatchAction, 
  MatchActionHandlers, 
  getMatchActions,
  getActionButtonStyle 
} from '@/utils/matchActions';

export interface MatchActionButtonsProps {
  scheduledDate: string | null;
  currentStatus: string;
  hasResults?: boolean;
  handlers: MatchActionHandlers;
  className?: string;
}

const MatchActionButtons: React.FC<MatchActionButtonsProps> = ({
  scheduledDate,
  currentStatus,
  hasResults = false,
  handlers,
  className = '',
}) => {
  const [loadingAction, setLoadingAction] = useState<string | null>(null);

  // Get available actions based on match status
  const actions = getMatchActions(scheduledDate, currentStatus, hasResults);

  const handleActionPress = async (action: MatchAction) => {
    // Show confirmation dialog if required
    if (action.requiresConfirmation && action.confirmationMessage) {
      Alert.alert(
        'Confirm Action',
        action.confirmationMessage,
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Confirm',
            style: action.type === 'danger' ? 'destructive' : 'default',
            onPress: () => executeAction(action),
          },
        ]
      );
    } else {
      executeAction(action);
    }
  };

  const executeAction = async (action: MatchAction) => {
    setLoadingAction(action.id);
    
    try {
      // Map action IDs to handler functions
      switch (action.id) {
        case 'start':
          await handlers.onStartMatch?.();
          break;
        case 'edit':
          await handlers.onEditMatch?.();
          break;
        case 'cancel':
          await handlers.onCancelMatch?.();
          break;
        case 'delete':
          await handlers.onDeleteMatch?.();
          break;
        case 'reschedule':
          await handlers.onRescheduleMatch?.();
          break;
        case 'mark_completed':
          await handlers.onMarkCompleted?.();
          break;
        case 'view_results':
          await handlers.onViewResults?.();
          break;
        case 'add_results':
          await handlers.onAddResults?.();
          break;
        case 'edit_results':
          await handlers.onEditResults?.();
          break;
        case 'resume':
          await handlers.onResumeMatch?.();
          break;
        case 'restart':
          await handlers.onRestartMatch?.();
          break;
        case 'uncancel':
          await handlers.onUncancelMatch?.();
          break;
        default:
          console.warn(`Unknown action: ${action.id}`);
      }
    } catch (error) {
      console.error(`Error executing action ${action.id}:`, error);
      Alert.alert(
        'Error',
        `Failed to ${action.title.toLowerCase()}. Please try again.`
      );
    } finally {
      setLoadingAction(null);
    }
  };

  const getButtonVariant = (type: MatchAction['type']) => {
    switch (type) {
      case 'primary':
        return 'solid';
      case 'secondary':
        return 'outline';
      case 'danger':
        return 'solid';
      case 'warning':
        return 'solid';
      default:
        return 'outline';
    }
  };

  const getButtonAction = (type: MatchAction['type']) => {
    switch (type) {
      case 'primary':
        return 'primary';
      case 'secondary':
        return 'secondary';
      case 'danger':
        return 'negative';
      case 'warning':
        return 'primary'; // Using primary for warning since there's no warning action
      default:
        return 'secondary';
    }
  };

  if (actions.length === 0) {
    return null;
  }

  return (
    <VStack className={`space-y-3 ${className}`}>
      {actions.map((action) => (
        <CTAButton
          key={action.id}
          title={action.title}
          onPress={() => handleActionPress(action)}
          variant={getButtonVariant(action.type)}
          action={getButtonAction(action.type)}
          isLoading={loadingAction === action.id}
          disabled={loadingAction !== null}
        />
      ))}
    </VStack>
  );
};

export default MatchActionButtons;
