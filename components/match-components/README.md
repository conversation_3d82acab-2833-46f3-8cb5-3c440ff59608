# MatchActions Component

A simple component that renders action buttons based on match status.

## Usage

```tsx
import MatchActions from '@/components/match-components/MatchActions';
import { MatchActionHandlers } from '@/utils/matchActions';

// Define your action handlers
const actionHandlers: MatchActionHandlers = {
  onStartMatch: () => {
    // Handle start match
    console.log('Starting match...');
  },
  onEditMatch: () => {
    // Handle edit match
    console.log('Editing match...');
  },
  onCancelMatch: () => {
    // Handle cancel match
    console.log('Cancelling match...');
  },
  onDeleteMatch: () => {
    // Handle delete match
    console.log('Deleting match...');
  },
  onRescheduleMatch: () => {
    // Handle reschedule match
    console.log('Rescheduling match...');
  },
  onMarkCompleted: () => {
    // Handle mark completed
    console.log('Marking match as completed...');
  },
  onViewResults: () => {
    // Handle view results
    console.log('Viewing results...');
  },
  onAddResults: () => {
    // Handle add results
    console.log('Adding results...');
  },
  onEditResults: () => {
    // Handle edit results
    console.log('Editing results...');
  },
  onRestartMatch: () => {
    // Handle restart match
    console.log('Restarting match...');
  },
  onUncancelMatch: () => {
    // Handle uncancel match
    console.log('Restoring match...');
  },
};

// Use the component
<MatchActions
  scheduledDate={match.scheduled_date}
  currentStatus={match.status}
  hasResults={!!(match.participant_1_score || match.participant_2_score)}
  handlers={actionHandlers}
  className="my-custom-class" // optional
/>
```

## Actions by Status

- **Scheduled**: Start Match, Edit Match, Reschedule, Cancel Match, Delete Match
- **Delayed**: Start Match, Reschedule (highlighted), Edit Match, Cancel Match  
- **In Progress**: Add Results, Mark Completed, Edit Match, Cancel Match
- **Completed**: View Results, Edit Results, Restart Match, Edit Match
- **Cancelled**: Restore Match, Edit Match, Delete Match

## Features

- ✅ Automatic action selection based on match status
- ✅ Built-in confirmation dialogs for dangerous actions
- ✅ Loading states during action execution
- ✅ Proper button styling (primary/secondary/danger)
- ✅ TypeScript support
- ✅ Customizable styling with className prop
