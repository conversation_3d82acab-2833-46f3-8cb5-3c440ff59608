import React from 'react';
import { View } from 'react-native';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Button, ButtonText } from '@/components/ui/button';
import { CTAButton } from '@/components/ui/primaryCTAbutton';
import MatchActions from './MatchActions';
import { MatchActionHandlers } from '@/utils/matchActions';

interface MatchActionExamplesProps {
  scheduledDate: string | null;
  currentStatus: string;
  hasResults?: boolean;
  handlers: MatchActionHandlers;
}

/**
 * Example component showing different ways to use MatchActions
 */
const MatchActionExamples: React.FC<MatchActionExamplesProps> = ({
  scheduledDate,
  currentStatus,
  hasResults = false,
  handlers,
}) => {
  return (
    <VStack className="space-y-6 p-4">
      
      {/* Example 1: Vertical Stack Layout (like in MatchDetailsDialog) */}
      <View>
        <MatchActions
          scheduledDate={scheduledDate}
          currentStatus={currentStatus}
          hasResults={hasResults}
          handlers={handlers}
        >
          {(actions, handleActionPress, loadingAction) => (
            <VStack className="space-y-3">
              {actions.map((action) => (
                <Button
                  key={action.id}
                  onPress={() => handleActionPress(action)}
                  variant={action.type === "secondary" ? "outline" : "solid"}
                  action={
                    action.type === "danger"
                      ? "negative"
                      : action.type === "secondary"
                      ? "secondary"
                      : "primary"
                  }
                  isDisabled={loadingAction !== null}
                  className="w-full"
                >
                  <ButtonText>{action.title}</ButtonText>
                </Button>
              ))}
            </VStack>
          )}
        </MatchActions>
      </View>

      {/* Example 2: Horizontal Layout (for toolbars) */}
      <View>
        <MatchActions
          scheduledDate={scheduledDate}
          currentStatus={currentStatus}
          hasResults={hasResults}
          handlers={handlers}
        >
          {(actions, handleActionPress, loadingAction) => (
            <HStack className="space-x-2 flex-wrap">
              {actions.slice(0, 3).map((action) => (
                <Button
                  key={action.id}
                  onPress={() => handleActionPress(action)}
                  variant={action.type === "secondary" ? "outline" : "solid"}
                  action={
                    action.type === "danger"
                      ? "negative"
                      : action.type === "secondary"
                      ? "secondary"
                      : "primary"
                  }
                  isDisabled={loadingAction !== null}
                  size="sm"
                  className="flex-1"
                >
                  <ButtonText>{action.title}</ButtonText>
                </Button>
              ))}
            </HStack>
          )}
        </MatchActions>
      </View>

      {/* Example 3: Using CTAButton for primary action only */}
      <View>
        <MatchActions
          scheduledDate={scheduledDate}
          currentStatus={currentStatus}
          hasResults={hasResults}
          handlers={handlers}
        >
          {(actions, handleActionPress, loadingAction) => {
            const primaryAction = actions.find(action => action.type === 'primary');
            const secondaryActions = actions.filter(action => action.type !== 'primary');
            
            return (
              <VStack className="space-y-3">
                {primaryAction && (
                  <CTAButton
                    title={primaryAction.title}
                    onPress={() => handleActionPress(primaryAction)}
                    loading={loadingAction === primaryAction.id}
                    isFormValid={loadingAction === null}
                  />
                )}
                
                {secondaryActions.length > 0 && (
                  <HStack className="space-x-2 justify-center">
                    {secondaryActions.slice(0, 2).map((action) => (
                      <Button
                        key={action.id}
                        onPress={() => handleActionPress(action)}
                        variant="outline"
                        action="secondary"
                        isDisabled={loadingAction !== null}
                        size="sm"
                      >
                        <ButtonText>{action.title}</ButtonText>
                      </Button>
                    ))}
                  </HStack>
                )}
              </VStack>
            );
          }}
        </MatchActions>
      </View>

      {/* Example 4: Custom styling with conditional rendering */}
      <View>
        <MatchActions
          scheduledDate={scheduledDate}
          currentStatus={currentStatus}
          hasResults={hasResults}
          handlers={handlers}
        >
          {(actions, handleActionPress, loadingAction) => (
            <VStack className="space-y-2">
              {actions.map((action, index) => {
                const isFirst = index === 0;
                const isDanger = action.type === 'danger';
                
                return (
                  <Button
                    key={action.id}
                    onPress={() => handleActionPress(action)}
                    variant={isFirst ? "solid" : "outline"}
                    action={
                      isDanger ? "negative" : 
                      isFirst ? "primary" : "secondary"
                    }
                    isDisabled={loadingAction !== null}
                    className={`w-full ${isDanger ? 'mt-4' : ''}`}
                  >
                    <ButtonText>
                      {loadingAction === action.id ? 'Loading...' : action.title}
                    </ButtonText>
                  </Button>
                );
              })}
            </VStack>
          )}
        </MatchActions>
      </View>
    </VStack>
  );
};

export default MatchActionExamples;
