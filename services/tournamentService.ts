import { supabase } from '@/lib/supabase';
import { transformTournamentFormData } from '@/utils/tournament-utils';
import { TournamentFormData } from '@/types/tournament';
import { getCurrentUser } from '@/utils/auth-utils';

interface SubmissionResult {
  success: boolean;
  error?: string;
  id?: string;
}

export async function submitTournament(
  formData: TournamentFormData
): Promise<SubmissionResult> {
  try {
    const { success, user, error: userError } = await getCurrentUser();

    if (!success || !user) {
      return {
        success: false,
        error: userError || 'User not authenticated',
      };
    }

    const transformedData = transformTournamentFormData(formData);

    const { error, data } = await supabase
      .from('tournaments')
      .insert([
        {
          ...transformedData,
          created_by: user.id,
        },
      ])
      .select('id')
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, id: data?.id || '' };
  } catch (err: any) {
    return { success: false, error: err.message || 'Unknown error occurred' };
  }
}

// Removed unused interfaces Options and FetchResult

export async function fetchUserTournaments({
  search = '',
  limit = 10,
  page = 1,
}: {
  search?: string;
  limit?: number;
  page?: number;
}) {
  try {
    const { success, user, error: userError } = await getCurrentUser();

    if (!success || !user) {
      return {
        success: false,
        error: userError || 'User not authenticated',
        data: [],
        count: 0,
      };
    }

    let query = supabase
      .from('user_tournaments_with_status')
      .select('*', { count: 'exact' })
      .eq('created_by', user.id)
      .order('status_rank', { ascending: true })
      .order('start_date', { ascending: true })
      .range((page - 1) * limit, page * limit - 1);

    if (search) {
      query = query.ilike('name', `%${search}%`);
    }

    const { data, count, error: queryError } = await query;

    if (queryError) {
      return {
        success: false,
        error: queryError.message,
        data: [],
        count: 0,
      };
    }

    return {
      success: true,
      data: data || [],
      count: count || 0,
    };
  } catch (err: any) {
    return {
      success: false,
      error: err.message || 'Something went wrong',
      data: [],
      count: 0,
    };
  }
}

import { Tournament } from '@/types/tournament';

export async function fetchTournamentById(tournamentId: string): Promise<{
  success: boolean;
  data?: Tournament;
  error?: string;
}> {
  try {
    const { data, error } = await supabase
      .from('tournaments')
      .select('*')
      .eq('id', tournamentId)
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, data };
  } catch (err: any) {
    return { success: false, error: err.message || 'Unknown error occurred' };
  }
}

import { TournamentUpdates } from '@/types/tournament';

export async function updateTournamentFields(
  tournamentId: string,
  updates: TournamentUpdates
): Promise<{ success: boolean; error?: string }> {
  const { error } = await supabase
    .from('tournaments')
    .update(updates)
    .eq('id', tournamentId);
  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true };
}

export async function deleteTournament(tournamentId: string) {
  const { data, error } = await supabase
    .from('tournaments')
    .delete()
    .eq('id', tournamentId);

  if (error) {
    return {
      success: false,
      error: error.message || 'Failed to delete tournament',
    };
  }

  return { success: true, data };
}

// Helper function for metadata operations
async function updateTournamentMetadata(
  tournamentId: string,
  metadataKey: string,
  newValue: string,
  itemType: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // Get current metadata
    const { data: tournament, error: fetchError } = await supabase
      .from('tournaments')
      .select('metadata')
      .eq('id', tournamentId)
      .single();

    if (fetchError) {
      return { success: false, error: fetchError.message };
    }

    const currentMetadata = tournament?.metadata || {};
    const currentItems = currentMetadata[metadataKey] || [];

    // Check if item already exists (case-insensitive)
    const itemExists = currentItems.some(
      (existingItem: string) =>
        existingItem.toLowerCase() === newValue.toLowerCase()
    );

    if (itemExists) {
      return {
        success: false,
        error: `${itemType} "${newValue}" already exists in this tournament`,
      };
    }

    // Add new item to the beginning of the array (custom items at top)
    const updatedItems = [newValue, ...currentItems];
    const updatedMetadata = {
      ...currentMetadata,
      [metadataKey]: updatedItems,
    };

    // Use existing updateTournamentFields function
    return await updateTournamentFields(tournamentId, {
      metadata: updatedMetadata,
    });
  } catch (err: any) {
    return { success: false, error: err.message || 'Unknown error occurred' };
  }
}

// Custom Round Management
export async function addCustomRound(
  tournamentId: string,
  roundName: string
): Promise<{ success: boolean; error?: string }> {
  return updateTournamentMetadata(
    tournamentId,
    'round_names',
    roundName,
    'Round'
  );
}

// Helper function for getting metadata
async function getTournamentMetadata(
  tournamentId: string,
  metadataKey: string
): Promise<{ success: boolean; items: string[]; error?: string }> {
  try {
    const { data: tournament, error } = await supabase
      .from('tournaments')
      .select('metadata')
      .eq('id', tournamentId)
      .single();

    if (error) {
      return { success: false, items: [], error: error.message };
    }

    const metadata = tournament?.metadata || {};
    const items = metadata[metadataKey] || [];

    return { success: true, items };
  } catch (err: any) {
    return {
      success: false,
      items: [],
      error: err.message || 'Unknown error occurred',
    };
  }
}

export async function getCustomRounds(
  tournamentId: string
): Promise<{ success: boolean; rounds: string[]; error?: string }> {
  const result = await getTournamentMetadata(tournamentId, 'round_names');
  return {
    success: result.success,
    rounds: result.items,
    error: result.error,
  };
}

// Court/Field Management
export async function addCustomCourtField(
  tournamentId: string,
  courtFieldName: string
): Promise<{ success: boolean; error?: string }> {
  return updateTournamentMetadata(
    tournamentId,
    'court_field_names',
    courtFieldName,
    'Court/Field'
  );
}

export async function getCustomCourtFields(
  tournamentId: string
): Promise<{ success: boolean; courtFields: string[]; error?: string }> {
  const result = await getTournamentMetadata(tournamentId, 'court_field_names');
  return {
    success: result.success,
    courtFields: result.items,
    error: result.error,
  };
}
