import { supabase } from '@/lib/supabase';
import { TeamFormData, TeamSocialLinks, TeamInfoData } from '@/types/teams';

export const checkIfTeamNameExists = async (
  tournamentId: string,
  name: string,
  excludeTeamId?: string
) => {
  let query = supabase
    .from('teams')
    .select('id')
    .eq('tournament_id', tournamentId)
    .ilike('name', name);

  if (excludeTeamId) {
    query = query.neq('id', excludeTeamId);
  }
  const { data, error } = await query;

  if (error) {
    return false;
  }

  return data.length > 0;
};

export const checkIfShortNameExists = async (
  tournamentId: string,
  shortName: string,
  excludeTeamId?: string
) => {
  let query = supabase
    .from('teams')
    .select('id')
    .eq('tournament_id', tournamentId)
    .ilike('short_name', shortName);

  // Exclude current team when editing
  if (excludeTeamId) {
    query = query.neq('id', excludeTeamId);
  }

  const { data, error } = await query;

  if (error) {
    return false;
  }

  return data.length > 0;
};

export const createTeam = async ({
  tournamentId,
  team,
}: {
  tournamentId: string;
  team: TeamFormData;
}) => {
  const { data: createdTeam, error } = await supabase
    .from('teams')
    .insert([
      {
        tournament_id: tournamentId,
        ...team,
      },
    ])
    .select()
    .single();

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true, team: createdTeam };
};

export const fetchTeamById = async (teamId: string) => {
  const { data: team, error } = await supabase
    .from('teams')
    .select('*')
    .eq('id', teamId)
    .single();

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true, team };
};

export const fetchPairById = async (pairId: string) => {
  try {
    const { data: team, error: teamError } = await supabase
      .from('teams')
      .select('*')
      .eq('id', pairId)
      .single();

    if (teamError) {
      return { success: false, error: teamError.message };
    }

    if (!team) {
      return { success: false, error: 'Pair not found' };
    }

    const { data: players, error: playersError } = await supabase
      .from('players')
      .select('id, name')
      .eq('team_id', pairId)
      .limit(2);

    if (playersError) {
      return { success: false, error: playersError.message };
    }

    const pairData = {
      id: team.id,
      name: team.name,
      player_1_name: players?.[0]?.name || 'Player 1',
      player_2_name: players?.[1]?.name || 'Player 2',
      player_1_id: players?.[0]?.id,
      player_2_id: players?.[1]?.id,
      created_at: team.created_at,
      tournament_id: team.tournament_id,
    };

    return { success: true, data: pairData };
  } catch (error: any) {
    return { success: false, error: error.message || 'Failed to fetch pair' };
  }
};

// Base function for fetching teams with optional search enhancement for pairs
const fetchTeamsBase = async ({
  tournamentId,
  search = '',
  page = 1,
  limit = 10,
  enhancedSearch = false,
}: {
  tournamentId: string;
  search?: string;
  page?: number;
  limit?: number;
  enhancedSearch?: boolean;
}) => {
  const offset = (page - 1) * limit;

  if (!search || !enhancedSearch) {
    // Standard team/pair fetching without enhanced search
    let baseQuery = supabase
      .from('teams')
      .select('*', { count: 'exact' })
      .eq('tournament_id', tournamentId);

    if (search) {
      baseQuery = baseQuery.ilike('name', `%${search}%`);
    }

    const { data, count, error } = await baseQuery
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      return { teams: [], count: 0, error: error.message };
    }

    return {
      teams: data,
      count: count || 0,
      error: null,
    };
  }

  // Enhanced search for pairs (search both team names and player names)
  const [teamsResult, playersResult] = await Promise.all([
    supabase
      .from('teams')
      .select('id')
      .eq('tournament_id', tournamentId)
      .ilike('name', `%${search}%`),
    supabase
      .from('players')
      .select('team_id')
      .eq('tournament_id', tournamentId)
      .not('team_id', 'is', null)
      .ilike('name', `%${search}%`),
  ]);

  if (teamsResult.error || playersResult.error) {
    return {
      teams: [],
      count: 0,
      error: teamsResult.error?.message || playersResult.error?.message,
    };
  }

  // Combine team IDs from both searches
  const teamIds = new Set([
    ...(teamsResult.data || []).map((team) => team.id),
    ...(playersResult.data || [])
      .map((player) => player.team_id)
      .filter(Boolean),
  ]);

  if (teamIds.size === 0) {
    return { teams: [], count: 0, error: null };
  }

  // Fetch the actual teams/pairs with pagination
  const { data, count, error } = await supabase
    .from('teams')
    .select('*', { count: 'exact' })
    .eq('tournament_id', tournamentId)
    .in('id', Array.from(teamIds))
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    return { teams: [], count: 0, error: error.message };
  }

  return {
    teams: data,
    count: count || 0,
    error: null,
  };
};

export const fetchTeams = async (params: {
  tournamentId: string;
  search?: string;
  page?: number;
  limit?: number;
}) => {
  return fetchTeamsBase({ ...params, enhancedSearch: false });
};

export const fetchPairs = async (params: {
  tournamentId: string;
  search?: string;
  page?: number;
  limit?: number;
}) => {
  const { tournamentId, search = '', page = 1, limit = 10 } = params;

  try {
    // First, get team IDs that have exactly 2 players
    const { data: teamIdsWithTwoPlayers, error: countError } = await supabase
      .from('players')
      .select('team_id')
      .eq('tournament_id', tournamentId)
      .not('team_id', 'is', null);

    if (countError) {
      return { teams: [], count: 0, error: countError.message };
    }

    // Count players per team and filter teams with exactly 2 players
    const playerCounts: Record<string, number> = {};
    (teamIdsWithTwoPlayers || []).forEach((player: any) => {
      playerCounts[player.team_id] = (playerCounts[player.team_id] || 0) + 1;
    });

    const validPairIds = Object.keys(playerCounts).filter(
      (teamId) => playerCounts[teamId] === 2
    );

    if (validPairIds.length === 0) {
      return { teams: [], count: 0, error: null };
    }

    // Now get the actual team data for valid pairs
    const { data: allValidPairs, error: teamsError } = await supabase
      .from('teams')
      .select('*')
      .eq('tournament_id', tournamentId)
      .in('id', validPairIds);

    if (teamsError) {
      return { teams: [], count: 0, error: teamsError.message };
    }

    let filteredPairs = allValidPairs || [];

    // Apply search filter if provided
    if (search) {
      // Enhanced search: search both team names and player names
      const [teamsResult, playersResult] = await Promise.all([
        supabase
          .from('teams')
          .select('id')
          .eq('tournament_id', tournamentId)
          .ilike('name', `%${search}%`),
        supabase
          .from('players')
          .select('team_id')
          .eq('tournament_id', tournamentId)
          .not('team_id', 'is', null)
          .ilike('name', `%${search}%`),
      ]);

      if (teamsResult.error || playersResult.error) {
        return {
          teams: [],
          count: 0,
          error: teamsResult.error?.message || playersResult.error?.message,
        };
      }

      // Combine team IDs from both searches
      const searchMatchingIds = new Set([
        ...(teamsResult.data || []).map((team) => team.id),
        ...(playersResult.data || [])
          .map((player) => player.team_id)
          .filter(Boolean),
      ]);

      // Filter pairs to only include those matching the search
      filteredPairs = filteredPairs.filter((pair) =>
        searchMatchingIds.has(pair.id)
      );
    }

    // Sort the filtered pairs
    const sortedPairs = filteredPairs.sort(
      (a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );

    // Apply pagination
    const offset = (page - 1) * limit;
    const totalCount = sortedPairs.length; // Count after filtering
    const paginatedPairs = sortedPairs.slice(offset, offset + limit);

    return {
      teams: paginatedPairs,
      count: totalCount, // This now reflects only pairs with exactly 2 players
      error: null,
    };
  } catch (error: any) {
    return {
      teams: [],
      count: 0,
      error: error.message || 'Failed to fetch pairs',
    };
  }
};

export const updateTeam = async ({
  teamId,
  team,
}: {
  teamId: string;
  team: TeamFormData | TeamSocialLinks | TeamInfoData;
}) => {
  const { data: updatedTeam, error } = await supabase
    .from('teams')
    .update(team)
    .eq('id', teamId)
    .select()
    .single();

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true, team: updatedTeam };
};

export const deleteTeamById = async (teamId: string) => {
  const { error } = await supabase.from('teams').delete().eq('id', teamId);

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true };
};

export const fetchTeamSocialLinksById = async (teamId: string) => {
  const { data, error } = await supabase
    .from('teams')
    .select('website, instagram_url, facebook_url')
    .eq('id', teamId)
    .single();

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true, socialLinks: data };
};
