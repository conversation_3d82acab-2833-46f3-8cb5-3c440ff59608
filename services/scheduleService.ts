import { supabase } from '@/lib/supabase';
import { Match } from '@/types/matches';
import { ROUND_CONFIG } from '@/config/roundConfig';

export interface RoundMatches {
  roundName: string;
  roundDisplayName: string;
  priority: number;
  matches: Match[];
}

export interface FetchRoundMatchesResult {
  roundMatches: RoundMatches[];
  totalMatches: number;
  error: string | null;
}

/**
 * Fetch all matches for a tournament grouped by round names
 * Rounds are sorted by priority from roundConfig.ts
 * Matches within each round are sorted by scheduled_date
 * Uses database-level filtering and sorting for better performance
 */
export async function fetchMatchesByRounds(
  tournamentId: string,
  tournamentRoundNames?: string[]
): Promise<FetchRoundMatchesResult> {
  try {
    // Get all unique round names from tournament metadata and existing matches
    const allRoundNames = new Set<string>();

    // Add round names from tournament metadata if provided
    if (tournamentRoundNames) {
      tournamentRoundNames.forEach((roundName: string) =>
        allRoundNames.add(roundName)
      );
    }

    // Get unique round names from existing matches to include any rounds not in metadata
    const { data: existingRounds, error: roundsError } = await supabase
      .from('matches')
      .select('stage')
      .eq('tournament_id', tournamentId)
      .not('stage', 'is', null);

    if (roundsError) {
      console.error(
        '[fetchMatchesByRounds] Error fetching round names:',
        roundsError
      );
    } else {
      existingRounds?.forEach((match) => {
        if (match.stage) {
          allRoundNames.add(match.stage);
        }
      });
    }

    // Create RoundMatches array with priority sorting
    const roundMatches: RoundMatches[] = [];
    let totalMatches = 0;

    // Fetch matches for each round using database queries with participant details
    for (const roundName of allRoundNames) {
      const { data: matches, error: matchesError } = await supabase
        .from('matches')
        .select('*')
        .eq('tournament_id', tournamentId)
        .eq('stage', roundName)
        .order('scheduled_date', { ascending: true, nullsFirst: false })
        .order('match_number', { ascending: true });

      if (matchesError) {
        console.error(
          `[fetchMatchesByRounds] Error fetching matches for round ${roundName}:`,
          matchesError
        );
        continue;
      }

      if (matches && matches.length > 0) {
        const roundConfig = ROUND_CONFIG.find(
          (config) => config.key === roundName || config.name === roundName
        );

        const roundDisplayName = roundConfig?.name || roundName;
        const priority = roundConfig?.priority || 999; // Unknown rounds get low priority

        roundMatches.push({
          roundName,
          roundDisplayName,
          priority,
          matches,
        });

        totalMatches += matches.length;
      }
    }

    // Handle matches without a round (stage is null)
    const { data: unassignedMatches, error: unassignedError } = await supabase
      .from('matches')
      .select('*')
      .eq('tournament_id', tournamentId)
      .is('stage', null)
      .order('scheduled_date', { ascending: true, nullsFirst: false })
      .order('match_number', { ascending: true });

    if (unassignedError) {
      console.error(
        '[fetchMatchesByRounds] Error fetching unassigned matches:',
        unassignedError
      );
    } else if (unassignedMatches && unassignedMatches.length > 0) {
      roundMatches.push({
        roundName: 'Unassigned',
        roundDisplayName: 'Unassigned',
        priority: 1000, // Lowest priority
        matches: unassignedMatches,
      });
      totalMatches += unassignedMatches.length;
    }

    // Sort rounds by priority (lower number = higher priority)
    roundMatches.sort((a, b) => a.priority - b.priority);

    return {
      roundMatches,
      totalMatches,
      error: null,
    };
  } catch (error: any) {
    console.error('[fetchMatchesByRounds] Unexpected error:', error);
    return {
      roundMatches: [],
      totalMatches: 0,
      error: error.message || 'Failed to fetch matches by rounds',
    };
  }
}

/**
 * Fetch matches for a specific round
 */
export async function fetchMatchesForRound(
  tournamentId: string,
  roundName: string
): Promise<{ matches: Match[]; error: string | null }> {
  try {
    const { data: matches, error } = await supabase
      .from('matches')
      .select('*')
      .eq('tournament_id', tournamentId)
      .eq('stage', roundName)
      .order('scheduled_date', { ascending: true });

    if (error) {
      console.error('[fetchMatchesForRound] Error:', error);
      return {
        matches: [],
        error: error.message,
      };
    }

    return {
      matches: matches || [],
      error: null,
    };
  } catch (error: any) {
    console.error('[fetchMatchesForRound] Unexpected error:', error);
    return {
      matches: [],
      error: error.message || 'Failed to fetch matches for round',
    };
  }
}
