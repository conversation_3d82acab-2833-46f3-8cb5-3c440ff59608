/**
 * Client-side match status utilities
 * Handles delayed status logic without backend storage
 */

/**
 * Check if a specific match should be marked as delayed
 * This is a client-side utility function
 */
export function shouldMatchBeDelayed(scheduledDate: string | null, currentStatus: string): boolean {
  if (!scheduledDate || currentStatus !== 'scheduled') {
    return false;
  }

  const now = new Date();
  const matchDate = new Date(scheduledDate);
  
  return matchDate < now;
}

/**
 * Get the appropriate status for a match based on its scheduled date
 * Returns 'delayed' for client-side display if scheduled time has passed
 */
export function getMatchStatusBasedOnDate(scheduledDate: string | null, currentStatus: string): string {
  if (shouldMatchBeDelayed(scheduledDate, currentStatus)) {
    return 'delayed';
  }
  return currentStatus;
}

/**
 * Get display text for match status including client-side delayed status
 */
export function getStatusDisplayText(status: string): string {
  switch (status) {
    case 'scheduled':
      return 'Scheduled';
    case 'delayed':
      return 'Delayed';
    case 'in_progress':
      return 'Live';
    case 'completed':
      return 'Completed';
    case 'cancelled':
      return 'Cancelled';
    default:
      return status?.toUpperCase() || 'Unknown';
  }
}

/**
 * Get color class for match status including client-side delayed status
 */
export function getStatusColor(status: string): string {
  switch (status) {
    case 'scheduled':
      return 'bg-blue-500';
    case 'delayed':
      return 'bg-orange-500';
    case 'in_progress':
      return 'bg-green-500';
    case 'completed':
      return 'bg-gray-500';
    case 'cancelled':
      return 'bg-red-500';
    default:
      return 'bg-gray-400';
  }
}
