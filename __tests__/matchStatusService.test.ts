import { 
  shouldMatchBeDelayed, 
  getMatchStatusBasedOnDate, 
  getStatusDisplayText, 
  getStatusColor 
} from '../services/matchStatusService';

describe('Match Status Service', () => {
  describe('shouldMatchBeDelayed', () => {
    it('should return true for past scheduled matches', () => {
      const pastDate = '2024-01-01T10:00:00Z';
      const result = shouldMatchBeDelayed(pastDate, 'scheduled');
      expect(result).toBe(true);
    });

    it('should return false for future scheduled matches', () => {
      const futureDate = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(); // tomorrow
      const result = shouldMatchBeDelayed(futureDate, 'scheduled');
      expect(result).toBe(false);
    });

    it('should return false for non-scheduled matches', () => {
      const pastDate = '2024-01-01T10:00:00Z';
      const result = shouldMatchBeDelayed(pastDate, 'completed');
      expect(result).toBe(false);
    });

    it('should return false for null scheduled date', () => {
      const result = shouldMatchBeDelayed(null, 'scheduled');
      expect(result).toBe(false);
    });
  });

  describe('getMatchStatusBasedOnDate', () => {
    it('should return delayed for past scheduled matches', () => {
      const pastDate = '2024-01-01T10:00:00Z';
      const result = getMatchStatusBasedOnDate(pastDate, 'scheduled');
      expect(result).toBe('delayed');
    });

    it('should return original status for future scheduled matches', () => {
      const futureDate = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString();
      const result = getMatchStatusBasedOnDate(futureDate, 'scheduled');
      expect(result).toBe('scheduled');
    });

    it('should return original status for non-scheduled matches', () => {
      const pastDate = '2024-01-01T10:00:00Z';
      const result = getMatchStatusBasedOnDate(pastDate, 'completed');
      expect(result).toBe('completed');
    });
  });

  describe('getStatusDisplayText', () => {
    it('should return correct text for all statuses', () => {
      expect(getStatusDisplayText('scheduled')).toBe('Scheduled');
      expect(getStatusDisplayText('delayed')).toBe('Delayed');
      expect(getStatusDisplayText('in_progress')).toBe('Live');
      expect(getStatusDisplayText('completed')).toBe('Completed');
      expect(getStatusDisplayText('cancelled')).toBe('Cancelled');
    });
  });

  describe('getStatusColor', () => {
    it('should return correct colors for all statuses', () => {
      expect(getStatusColor('scheduled')).toBe('bg-blue-500');
      expect(getStatusColor('delayed')).toBe('bg-orange-500');
      expect(getStatusColor('in_progress')).toBe('bg-green-500');
      expect(getStatusColor('completed')).toBe('bg-gray-500');
      expect(getStatusColor('cancelled')).toBe('bg-red-500');
      expect(getStatusColor('unknown')).toBe('bg-gray-400');
    });
  });
});
