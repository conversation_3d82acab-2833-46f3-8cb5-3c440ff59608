import { TeamFormData, TeamSocialLinks, TeamInfoData } from './teams';

export interface Player {
  id: string;
  name: string;
  jersey_number?: string;
}

export interface PairFormData {
  jersey_color?: string;
}

export interface Pair extends PairFormData, TeamInfoData {
  id: string;
  tournament_id: string;
  player1?: Player;
  player2?: Player;
}

export interface PairDisplayData {
  id: string;
  player1: Player;
  player2: Player;
  jersey_color?: string;
  name?: string;
  short_name?: string;
  logo_url?: string;
}

// Utility function to get initial pair form data
export const getInitialPairFormData = (pair?: Pair): PairFormData => {
  return {
    jersey_color: pair?.jersey_color || '',
  };
};
