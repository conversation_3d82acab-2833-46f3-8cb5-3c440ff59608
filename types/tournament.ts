import { TOURNAMENT_FORMATS } from '@/config/scheduleFormConfig';

export interface TournamentFormData {
  name: string;
  start_date: string;
  end_date: string;
  location?: string;
  sport?: string;
}

export type TournamentFormat =
  (typeof TOURNAMENT_FORMATS)[keyof typeof TOURNAMENT_FORMATS];

export interface TournamentUpdates {
  name?: string;
  start_date?: string;
  end_date?: string;
  location_description?: string;
  sport_type?: string;
  format?: TournamentFormat;
  tournament_rules?: Record<string, any>;
  metadata?: Record<string, any>; // NEW: For custom rounds and court fields
}

export interface Tournament {
  id: string;
  name: string;
  start_date?: string;
  end_date?: string;
  location_description?: string;
  sport_type: string;
  format?: TournamentFormat;
  tournament_rules?: Record<string, any>;
  mactch_rules?: Record<string, any>;
  created_by: string;
  created_at?: string;
  logo_url?: string;
  venue?: string;
  public?: boolean;
  display_name?: string;
  metadata?: Record<string, any>; // NEW: For custom rounds and court fields
}
