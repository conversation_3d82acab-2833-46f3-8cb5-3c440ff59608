// Participant type constants and enums
export const PARTICIPANT_TYPES = {
  PLAYER: 'player',
  PAIR: 'pair',
  TEAM: 'team',
} as const;

// Base participant type derived from constants
export type ParticipantType =
  (typeof PARTICIPANT_TYPES)[keyof typeof PARTICIPANT_TYPES];

// Base participant interface
interface BaseParticipant {
  id: string;
  name: string;
}

// Team participant interface
export interface TeamParticipant extends BaseParticipant {
  type: 'team';
  short_name?: string;
  logo_url?: string;
  captain_id?: string;
}

// Player participant interface
export interface PlayerParticipant extends BaseParticipant {
  type: 'player';
  jersey_number?: number;
  position?: string;
  team_id?: string;
}

// Pair participant interface
export interface PairParticipant extends BaseParticipant {
  type: 'pair';
  player_1_name: string;
  player_2_name: string;
  player_1_id?: string;
  player_2_id?: string;
}

// Union type for all participant details
export type ParticipantDetails =
  | TeamParticipant
  | PlayerParticipant
  | PairParticipant;

// Cache interface for storing participant data
export interface ParticipantCache {
  [key: string]: {
    data: ParticipantDetails;
    timestamp: number;
  };
}

// Utility type for participant service configuration
export interface ParticipantServiceConfig<T extends ParticipantDetails> {
  fetch: (id: string) => Promise<any>;
  transform: (data: any) => T;
  dataKey: string;
}

// Type for participant services mapping
export type ParticipantServices = {
  [K in ParticipantType]: ParticipantServiceConfig<
    K extends 'team'
      ? TeamParticipant
      : K extends 'player'
      ? PlayerParticipant
      : K extends 'pair'
      ? PairParticipant
      : never
  >;
};

// Hook interfaces
export interface UseParticipantDetailsProps {
  participantId: string | null;
  participantType: ParticipantType;
  participantName?: string | null;
  enableCache?: boolean;
  cacheExpiryMs?: number;
}

export interface UseParticipantDetailsReturn {
  participant: ParticipantDetails | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  clearCache: () => void;
}

// Utility interfaces for tournament rules (used by utility functions)
export interface TournamentRules {
  game_format?: string;
}

export interface TournamentWithRules {
  tournament_rules?: TournamentRules;
}
