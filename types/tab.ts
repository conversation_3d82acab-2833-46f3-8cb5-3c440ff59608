import { ReactNode, ReactElement } from 'react';

export interface TabItem<T = any> {
  id: string;
  name: string;
  render: (data: T) => ReactNode;
  onRefresh?: () => Promise<void> | void;
}

export interface TabBarConfig {
  scrollEnabled?: boolean;
  minWidth?: number;
  paddingVertical?: number;
  paddingHorizontal?: number;
  fontFamily?: string;
  fontWeight?:
    | '100'
    | '200'
    | '300'
    | '400'
    | '500'
    | '600'
    | '700'
    | '800'
    | '900'
    | 'bold'
    | 'normal';
  textTransform?: 'none' | 'capitalize' | 'uppercase' | 'lowercase';
  textAlign?: 'left' | 'center' | 'right';
  margin?: number;
  tabStyle?: {
    minWidth?: number;
    alignItems?: 'flex-start' | 'center' | 'flex-end';
    justifyContent?: 'flex-start' | 'center' | 'flex-end';
  };
  contentContainerStyle?: {
    backgroundColor?: string;
  };
}

export interface CollapsibleTabViewProps<T = any> {
  data: T;
  tabs: TabItem<T>[];
  tabBarConfig?: TabBarConfig;
  lazy?: boolean;
  renderHeader?: () => ReactElement | null;
  headerHeight?: number;
  initialTabName?: string;
  onTabChange?: (params: { tabName: string }) => void;
  showsVerticalScrollIndicator?: boolean;
  tabFilter?: (tab: TabItem<T>, data: T) => boolean;
  snapThreshold?: number | null;
  revealHeaderOnScroll?: boolean;
  cancelLazyFadeIn?: boolean;
  allowHeaderOverscroll?: boolean;
  minHeaderHeight?: number;
  pagerProps?: {
    scrollEnabled?: boolean;
    animationEnabled?: boolean;
    [key: string]: any;
  };
  enablePullToRefresh?: boolean;
  onRefresh?: () => Promise<void> | void;
  refreshing?: boolean;
}
