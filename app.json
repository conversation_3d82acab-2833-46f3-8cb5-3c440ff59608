{"expo": {"name": "Kali", "slug": "kali", "version": "0.1.0", "orientation": "portrait", "icon": "./assets/app-icons/1024x1024.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "in.kali", "infoPlist": {"NSLocationAlwaysAndWhenInUseUsageDescription": "This app requires access to your location to provide personalized content and features."}}, "expo": {"scheme": "kali", "deepLinking": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/app-icons/1024x1024.png", "backgroundColor": "#ffffff"}, "package": "in.kali", "versionCode": 4, "permissions": ["INTERNET", "ACCESS_NETWORK_STATE"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/app-icons/32x32.png"}, "plugins": ["expo-router", ["expo-font", {"fonts": ["node_modules/@expo-google-fonts/urbanist/400Regular/Urbanist_400Regular.ttf", "node_modules/@expo-google-fonts/urbanist/500Medium/Urbanist_500Medium.ttf", "node_modules/@expo-google-fonts/urbanist/600SemiBold/Urbanist_600SemiBold.ttf", "node_modules/@expo-google-fonts/urbanist/700Bold/Urbanist_700Bold.ttf", "node_modules/@expo-google-fonts/urbanist/800ExtraBold/Urbanist_800ExtraBold.ttf", "node_modules/@expo-google-fonts/urbanist/900Black_Italic/Urbanist_900Black_Italic.ttf"]}], ["@react-native-google-signin/google-signin", {"iosUrlScheme": "com.googleusercontent.apps.394214528477-72jo6lr9t81vm9vdk3n1jr1hvm1n0c6r"}]], "experiments": {"typedRoutes": true}, "updates": {"url": "https://u.expo.dev/304fd2c6-f889-45af-b8e8-448f8d3fed2d"}, "runtimeVersion": {"policy": "appVersion"}, "extra": {"router": {"origin": false}, "eas": {"projectId": "304fd2c6-f889-45af-b8e8-448f8d3fed2d"}}, "owner": "kali-app"}}