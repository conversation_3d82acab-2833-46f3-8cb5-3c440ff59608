const SCREENS = {
  // Tabs
  HOME: '/',
  EXPLORE: '/(tabs)/explore',
  MANAGE: '/(tabs)/manage',
  ACCOUNT: '/(tabs)/account',

  // Auth Flow
  FORGOT_PASSWORD: '/account/forgot-password',
  LOGI<PERSON>: '/account/login',
  SIGNUP: '/account/sign-up',
  SET_NEW_PASSWORD: '/account/set-new-password',
  TERMS_AND_CONDITIONS: '/account/terms-and-conditions',
  PRIVACY_POLICY: '/account/privacy-policy',
  PROFILE_SETTINGS: '/account/profile-settings',

  // Form Create Flow
  FORM_CREATE: '/screens/create/[index]',

  // Tournament Flow
  TOURNAMENT_VIEW: '/screens/tournament-view/[tournament-id]',
  CREATE_TOURNAMENT_INTRO: '/screens/create-tournament-intro',
  TOURNAMENT_SETTINGS: '/screens/tournament-settings/[tournament-id]',
  TOURNAMENT_RULES: '/screens/tournament/[tournament-id]/rules',

  // Teams Flow
  TEAMS_CREATE: '/screens/teams/create',
  TEAM_SQUAD: '/screens/teams/[team-id]/squad',
  TEAM_VIEW: '/screens/teams/[team-id]/view',
  TEAM_EDIT: '/screens/teams/[team-id]/edit',
  TEAM_INFO: '/screens/teams/[team-id]/team-info',

  // Pairs Flow
  PAIRS_CREATE: '/screens/pairs/create',
  PAIR_VIEW: '/screens/pairs/[pair-id]/view',
  PAIR_EDIT: '/screens/pairs/[pair-id]/edit',

  // Player
  PLAYER_VIEW: '/screens/player/[player-id]/view',
  PLAYER_EDIT: '/screens/player/[player-id]/edit',

  // View All Flow
  VIEW_ALL: '/screens/view-all',

  // Match Flow
  CREATE_MATCH_INTRO: '/screens/create-match-intro',
  CREATE_TOURNAMENT_MATCH: '/screens/tournament-match/create',

  //Schedule Flow
  SCHEDULE_CREATE: '/screens/schedule/create',

  //Location Screens
  LOCATION_SEARCH: '/screens/location-search',

  // Other Screens
  NO_INTERNET: '/screens/no-internet',
};

export default SCREENS;
