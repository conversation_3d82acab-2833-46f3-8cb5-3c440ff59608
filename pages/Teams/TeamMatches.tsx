import React from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import { type Team } from '@/types/teams';
import { VStack } from '@/components/ui/vstack';
import { Icon } from '@/components/ui/icon';
import { Calendar, Clock, MapPin } from 'lucide-react-native';

interface TeamMatchesProps {
  team: Team;
  tournamentId: string;
}

const TeamMatches: React.FC<TeamMatchesProps> = ({ team, tournamentId }) => {
  return (
    <VStack className="px-4 space-y-6 pb-6 gap-5 py-6">
      {/* Upcoming Matches */}
      <View className="bg-white rounded-lg p-4 shadow-sm border border-outline-100">
        <View className="flex-row items-center gap-2 mb-4">
          <Icon as={Calendar} size="sm" className="text-primary-0" />
          <Text className="text-lg font-urbanistSemiBold text-typography-900">
            Upcoming Matches
          </Text>
        </View>

        <Text className="text-base text-typography-600 text-center py-8 font-urbanist">
          No upcoming matches scheduled for this team.
        </Text>
      </View>

      {/* Recent Matches */}
      <View className="bg-white rounded-lg p-4 shadow-sm border border-outline-100">
        <View className="flex-row items-center gap-2 mb-4">
          <Icon as={Clock} size="sm" className="text-primary-0" />
          <Text className="text-lg font-urbanistSemiBold text-typography-900">
            Recent Matches
          </Text>
        </View>

        <Text className="text-base text-typography-600 text-center py-8 font-urbanist">
          No recent matches found for this team.
        </Text>
      </View>

      {/* Match History */}
      <View className="bg-white rounded-lg p-4 shadow-sm border border-outline-100">
        <View className="flex-row items-center gap-2 mb-4">
          <Icon as={MapPin} size="sm" className="text-primary-0" />
          <Text className="text-lg font-urbanistSemiBold text-typography-900">
            Match History
          </Text>
        </View>

        <VStack className="space-y-4 gap-2">
          <View className="p-3 bg-background-50 rounded-lg">
            <Text className="text-sm font-urbanistMedium text-typography-600 mb-2">
              Total Matches
            </Text>
            <Text className="text-2xl font-urbanistBold text-typography-900">
              0
            </Text>
          </View>

          <View className="flex-row space-x-4 gap-2">
            <View className="flex-1 p-3 bg-success-50 rounded-lg">
              <Text className="text-sm font-urbanistMedium text-success-700 mb-1">
                Wins
              </Text>
              <Text className="text-xl font-urbanistBold text-success-800">
                0
              </Text>
            </View>

            <View className="flex-1 p-3 bg-error-50 rounded-lg">
              <Text className="text-sm font-urbanistMedium text-error-700 mb-1">
                Losses
              </Text>
              <Text className="text-xl font-urbanistBold text-error-800">
                0
              </Text>
            </View>
          </View>

          <Text className="text-sm text-typography-500 text-center mt-4 font-urbanist">
            Match history will be populated as the team participates in
            tournament matches.
          </Text>
        </VStack>
      </View>
    </VStack>
  );
};

export default TeamMatches;
