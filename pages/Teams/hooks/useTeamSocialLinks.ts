import { useState, useCallback } from 'react';
import { toast } from '@/toast/toast';
import { updateTeam, fetchTeamSocialLinksById } from '@/services/teamsService';
import {
  type SocialPlatform,
  SocialPlatformKeyMap,
} from '@/components/k-components/SocialLinkEditor';
import { useFocusEffect } from '@react-navigation/native';
import { TeamSocialLinks } from '@/types/teams';

export const useTeamSocialLinks = (teamId: string) => {
  const [socialLinks, setSocialLinks] = useState<TeamSocialLinks | undefined>(
    undefined
  );
  const [loading, setLoading] = useState(false);

  const fetchLinks = useCallback(async () => {
    setLoading(true);
    const { success, socialLinks, error } = await fetchTeamSocialLinksById(
      teamId
    );
    if (success) {
      setSocialLinks(socialLinks);
    } else {
      toast.error(error || 'Failed to fetch social links');
    }
    setLoading(false);
  }, [teamId]);

  const handleSocialLinkSave = async (
    platform: SocialPlatform,
    url: string
  ) => {
    const key = SocialPlatformKeyMap[platform];
    const { success, error } = await updateTeam({
      teamId,
      team: { [key]: url },
    });

    if (success) {
      toast.success('Updated successfully');
      await fetchLinks();
    } else {
      toast.error(error || 'Failed to update social link');
    }

    return { success, error };
  };

  useFocusEffect(
    useCallback(() => {
      fetchLinks();
    }, [fetchLinks])
  );

  return {
    socialLinks,
    handleSocialLinkSave,
    refetchSocialLinks: fetchLinks,
    loading,
  };
};
