import { useState, useCallback } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import { fetchTeamById } from '@/services/teamsService';
import { toast } from '@/toast/toast';
import { type Team } from '@/types/teams';

interface UseTournamentTeamProps {
  teamId: string;
  autoFetch?: boolean;
}

interface UseTournamentTeamReturn {
  team: Team | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useTournamentTeam = ({
  teamId,
  autoFetch = true,
}: UseTournamentTeamProps): UseTournamentTeamReturn => {
  const [team, setTeam] = useState<Team | null>(null);
  const [loading, setLoading] = useState(autoFetch);
  const [error, setError] = useState<string | null>(null);

  const fetchTeam = useCallback(async () => {
    if (!teamId) return;

    setLoading(true);
    setError(null);

    try {
      const { success, team: fetchedTeam, error: fetchError } = await fetchTeamById(teamId);

      if (!success) {
        setError(fetchError || 'Failed to load team details');
        setTeam(null);
      } else {
        setTeam(fetchedTeam);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch team');
      setTeam(null);
    } finally {
      setLoading(false);
    }
  }, [teamId]);

  useFocusEffect(
    useCallback(() => {
      if (autoFetch) {
        fetchTeam();
      }
    }, [fetchTeam, autoFetch])
  );

  return {
    team,
    loading,
    error,
    refetch: fetchTeam,
  };
};