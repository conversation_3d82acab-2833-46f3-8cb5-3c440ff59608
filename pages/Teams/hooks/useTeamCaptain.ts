import { useState, useCallback, useMemo } from 'react';
import { updateTeam } from '@/services/teamsService';
import { toast } from '@/toast/toast';
import { type Player } from '@/types/player';
import { type Option } from '@/components/k-components/AsyncSelectWithSearch';

interface UseTeamCaptainProps {
  teamId: string;
  players: Player[];
  currentCaptainId?: string;
  onCaptainUpdate: (captainId: string | null) => void;
}

interface UseTeamCaptainReturn {
  showCaptainModal: boolean;
  isUpdating: boolean;
  playerOptions: Option[];
  filteredPlayerOptions: Option[];
  openCaptainModal: () => void;
  closeCaptainModal: () => void;
  handleCaptainSelect: (selected: Option | Option[] | null) => Promise<void>;
  handleSearchChange: (query: string) => void;
}

export function useTeamCaptain({
  teamId,
  players,
  currentCaptainId,
  onCaptainUpdate,
}: UseTeamCaptainProps): UseTeamCaptainReturn {
  const [showCaptainModal, setShowCaptainModal] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Convert players to options for AsyncSelectWithSearch, excluding current captain
  const playerOptions = useMemo(() => {
    return players
      .filter((player) => player.id !== currentCaptainId) // Exclude current captain
      .map((player) => ({
        label: player.name,
        value: player.id,
        player,
      }));
  }, [players, currentCaptainId]);

  // Filter options based on search query
  const filteredPlayerOptions = useMemo(() => {
    if (!searchQuery.trim()) {
      return playerOptions;
    }

    return playerOptions.filter((option) =>
      option.label.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [playerOptions, searchQuery]);

  const openCaptainModal = useCallback(() => {
    setShowCaptainModal(true);
  }, []);

  const closeCaptainModal = useCallback(() => {
    setShowCaptainModal(false);
    setSearchQuery(''); // Reset search when closing modal
  }, []);

  const handleSearchChange = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const handleCaptainSelect = useCallback(
    async (selected: Option | Option[] | null) => {
      if (!selected || Array.isArray(selected)) {
        return;
      }

      setIsUpdating(true);
      try {
        const { success, error } = await updateTeam({
          teamId,
          team: { captain_id: selected.value },
        });

        if (!success) {
          toast.error(error || 'Failed to set team captain');
          return;
        }

        // Update local state
        onCaptainUpdate(selected.value);
        toast.success(`${selected.label} is now the team captain`);
        closeCaptainModal();
      } catch (error) {
        toast.error('Something went wrong');
      } finally {
        setIsUpdating(false);
      }
    },
    [teamId, onCaptainUpdate, closeCaptainModal]
  );

  return {
    showCaptainModal,
    isUpdating,
    playerOptions,
    filteredPlayerOptions,
    openCaptainModal,
    closeCaptainModal,
    handleCaptainSelect,
    handleSearchChange,
  };
}
