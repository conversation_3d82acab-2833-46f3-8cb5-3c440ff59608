import { useState } from 'react';
import { useRouter } from 'expo-router';
import { deleteTeamById } from '@/services/teamsService';
import { toast } from '@/toast/toast';
import { type Team } from '@/types/teams';

export function useTeamDeletion() {
  const [deleting, setDeleting] = useState(false);
  const router = useRouter();

  const handleDeleteTeam = async (team: Team) => {
    if (!team) return { success: false, error: 'No team provided' };

    setDeleting(true);
    
    try {
      const result = await deleteTeamById(team.id);
      
      if (result.success) {
        toast.success('Team removed successfully');
        router.back();
        return { success: true };
      } else {
        toast.error(result.error || 'Failed to remove team');
        return { success: false, error: result.error };
      }
    } catch (error) {
      const errorMessage = 'An unexpected error occurred';
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setDeleting(false);
    }
  };

  return {
    deleting,
    handleDeleteTeam,
  };
}
