import React, { useState, useMemo } from 'react';
import { ScrollView } from 'react-native';
import { VStack } from '@/components/ui/vstack';
import FormField, { FieldType } from '@/components/k-components/FormField';
import { CTAButton } from '@/components/ui/primaryCTAbutton';
import { ErrorText } from '@/components/ui/errortext';
import { createTeamFormFields } from '@/config/teamFormConfig';
import { type TeamFormData } from '@/types/teams';
import {
  getInitialFormData,
  validateTeamForm,
  generateShortName,
} from './utils/teamFormUtils';

interface TeamFormProps {
  initialData?: any;
  onSubmit: (formData: TeamFormData) => Promise<void>;
  submitButtonText: string;
  isLoading?: boolean;
  tournamentId: string;
  isEditMode?: boolean;
}

const TeamForm: React.FC<TeamFormProps> = ({
  initialData,
  onSubmit,
  submitButtonText,
  isLoading = false,
  tournamentId,
  isEditMode = false,
}) => {
  const [form, setForm] = useState<TeamFormData>(() =>
    getInitialFormData(initialData)
  );
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [apiError, setApiError] = useState<string | null>(null);

  const formFields = useMemo(
    () => createTeamFormFields(tournamentId, initialData?.id),
    [tournamentId, initialData?.id]
  );

  const handleChange = (key: string, value: any) => {
    setForm((prev) => {
      const newForm = { ...prev, [key]: value };

      // Only auto-generate short name in create mode (not edit mode)
      if (
        !isEditMode &&
        key === 'name' &&
        (!prev.short_name || prev.short_name === generateShortName(prev.name))
      ) {
        newForm.short_name = generateShortName(value);
      }

      return newForm;
    });
    setErrors((prev) => ({ ...prev, [key]: '' }));
    setApiError(null);
  };

  const handleSubmit = async () => {
    setErrors({});
    setApiError(null);

    const validationErrors = validateTeamForm(form);

    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      await onSubmit(form);
    } catch (error: any) {
      setApiError(error.message || 'An error occurred');
    }
  };

  const renderFormField = (field: any) => (
    <FormField
      key={field.key}
      type={field.type as FieldType}
      keyName={field.key}
      field={field}
      value={form[field.key as keyof TeamFormData]}
      error={errors[field.key]}
      onChange={handleChange}
      inputprops={
        field.key === 'short_name' ? { autoCapitalize: 'characters' } : {}
      }
    />
  );

  return (
    <ScrollView showsVerticalScrollIndicator={false}>
      <VStack className="px-4 py-4 space-y-4 w-full">
        {formFields.map(renderFormField)}

        <CTAButton
          title={submitButtonText}
          onPress={handleSubmit}
          loading={isLoading}
        />
        <ErrorText message={apiError} />
      </VStack>
    </ScrollView>
  );
};

export default TeamForm;
