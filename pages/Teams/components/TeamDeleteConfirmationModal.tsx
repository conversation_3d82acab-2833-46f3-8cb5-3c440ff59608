import React from 'react';
import { <PERSON><PERSON>, ModalBackdrop, ModalContent } from '@/components/ui/modal';
import ConfirmationPrompt from '@/components/k-components/ConfirmationPrompt';
import { useTeamDeletion } from '@/pages/Teams/hooks/useTeamDeletion';
import { type Team } from '@/types/teams';

interface TeamDeleteConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  team: Team | null;
}

const TeamDeleteConfirmationModal: React.FC<
  TeamDeleteConfirmationModalProps
> = ({ isOpen, onClose, team }) => {
  const { deleting, handleDeleteTeam } = useTeamDeletion();

  const onDeleteConfirm = async () => {
    if (!team) return;

    const result = await handleDeleteTeam(team);
    if (result.success) {
      onClose();
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalBackdrop />
      <ModalContent className="p-6">
        <ConfirmationPrompt
          heading="Delete Team?"
          description="This action cannot be undone. Deleting this team will remove all associated data."
          primaryText="Delete"
          secondaryText="Cancel"
          loading={deleting}
          onPrimaryPress={onDeleteConfirm}
          onSecondaryPress={onClose}
          type="error"
        />
      </ModalContent>
    </Modal>
  );
};

export default TeamDeleteConfirmationModal;
