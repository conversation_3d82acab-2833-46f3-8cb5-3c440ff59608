import React, { memo, useCallback } from 'react';
import { View, Text, Pressable } from 'react-native';
import LogoImage from '@/components/k-components/LogoImage';
import { Divider } from '@/components/ui/divider';
import { useRouter } from 'expo-router';
import SCREENS from '@/constants/Screens';
import { type Team } from '@/types/teams';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';

interface TeamListCardProps {
  team: Team;
  tournamentId: string;
}

export const TeamListCard = memo<TeamListCardProps>(
  ({ team, tournamentId }) => {
    const router = useRouter();

    const handlePress = useCallback(() => {
      router.push({
        pathname: SCREENS.TEAM_VIEW,
        params: {
          'team-id': team.id,
          'tournament-id': tournamentId,
        },
      });
    }, [router, team.id, tournamentId]);

    return (
      <>
        <Pressable
          className="flex-row items-center justify-between py-3 px-2"
          onPress={handlePress}
        >
          <HStack className="items-center space-x-3 flex-1">
            <LogoImage
              logoUrl={team.logo_url}
              width={40}
              height={40}
              borderRadius={20}
              fallbackText={team?.name || ''}
              fallBacktextClassName={'text-lg font-urbanistBold'}
            />
            <VStack className="flex-1 ml-3">
              <Text
                numberOfLines={1}
                ellipsizeMode="tail"
                className="text-base font-urbanistMedium text-typography-800"
              >
                {team.name}
              </Text>
              <Text className="text-[10px] text-typography-600">
                {team.short_name}
              </Text>
            </VStack>
          </HStack>
        </Pressable>
        <Divider />
      </>
    );
  }
);
