import React from 'react';
import { Text } from '@/components/ui/text';
import { isEmpty } from 'lodash';

interface TeamMottoProps {
  motto: string | null | undefined;
}

export const TeamMotto: React.FC<TeamMottoProps> = ({ motto }) => {
  if (isEmpty(motto)) return null;

  return (
    <Text
      className="font-urbanistBlackItalic text-center text-typography-500 text-lg px-4 tracking-wider"
      numberOfLines={2}
      ellipsizeMode="tail"
    >
      “{motto}”
    </Text>
  );
};
