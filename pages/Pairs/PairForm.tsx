import React, { useState, useMemo, useEffect, memo } from 'react';
import { ScrollView, View, Pressable } from 'react-native';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import FormField, { FieldType } from '@/components/k-components/FormField';
import { CTAButton } from '@/components/ui/primaryCTAbutton';
import { createPairFormFields } from '@/config/pairFormConfig';
import { type PairFormData } from '@/types/pairs';
import { Text } from '@/components/ui/text';
import { Icon } from '@/components/ui/icon';
import { Button, ButtonText, ButtonIcon } from '@/components/ui/button';
import { UserPlusIcon, PlusIcon, XIcon } from 'lucide-react-native';
import { AsyncSelectWithSearch } from '@/components/k-components/AsyncSelectWithSearch';
import { PlayerOptionRenderer } from '@/components/k-components/PlayerOptionRenderer';
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
} from '@/components/ui/actionsheet';
import AddPlayer from '@/pages/Players/AddPlayer';
import { type Option } from '@/components/k-components/AsyncSelectWithSearch';
import { type Player } from '@/types/player';

import { triggerHapticFeedback } from '@/utils';
import { type Tournament } from '@/pages/TournamentViewScreen/types';
import LogoImage from '@/components/k-components/LogoImage';
import { usePairPlayerManagement } from './hooks/usePairPlayerManagement';

interface PairFormProps {
  pairId?: string; // For edit mode
  tournament: Tournament;
  onSubmit: (teamId: string) => void;
  submitButtonText: string;
  isLoading?: boolean;
  initialFormData?: PairFormData; // Initial form data for edit mode
  initialPlayers?: Player[]; // Initial players for edit mode
}

const PairForm: React.FC<PairFormProps> = ({
  pairId,
  tournament,
  onSubmit,
  submitButtonText,
  isLoading = false,
  initialFormData,
  initialPlayers = [],
}) => {
  const [formData, setFormData] = useState<PairFormData>(
    initialFormData || { jersey_color: '' }
  );
  const [showAddPlayer, setShowAddPlayer] = useState(false);
  const [showSelectPlayer, setShowSelectPlayer] = useState(false);

  const [playerOperations, setPlayerOperations] = useState<Set<string>>(
    new Set()
  );

  // Use the pair player management hook with edit mode support
  const {
    selectedPlayers,
    localPlayers,
    isCreatingPair,
    availablePlayers,
    hasMorePlayers,
    addLocalPlayer,
    removeLocalPlayer,
    addSelectedPlayers,
    removeSelectedPlayer,
    getAllPlayers,
    canAddMorePlayers,
    getPlayerCount,
    createPair,
    loadMorePlayers,
    handleSearchChange,
    resetAvailablePlayers,
  } = usePairPlayerManagement({
    tournament,
    pairId,
    initialPlayers,
  });

  const pairFields = useMemo(() => createPairFormFields(), []);

  const handleAddLocalPlayer = async (playerData: any) => {
    addLocalPlayer(playerData);
  };

  const handlePlayerSelect = (option: Option | Option[] | null) => {
    if (!option) return;

    // 1. normalize to an array of Option
    const picked = Array.isArray(option) ? option : [option];

    // 2. Extract Player objects from Options (Options contain player property)
    const playersToAdd = picked
      .map((opt) => opt.player as Player)
      .filter(Boolean);

    // 3. Add selected players using the hook
    addSelectedPlayers(playersToAdd);
  };

  const handlePlayerSelectAndClose = (selected: Option | Option[] | null) => {
    handlePlayerSelect(selected);
    setShowSelectPlayer(false);
  };

  const handleRemovePlayer = (playerId: string) => {
    triggerHapticFeedback();
    setPlayerOperations((prev) => new Set(prev).add(playerId));

    setTimeout(() => {
      // Check if it's a selected player or local player
      if (selectedPlayers.some((p) => p.id === playerId)) {
        removeSelectedPlayer(playerId);
      } else if (localPlayers.some((p) => p.tempId === playerId)) {
        removeLocalPlayer(playerId);
      }

      // Update form data
      setFormData((prev) => ({
        ...prev,
      }));

      setPlayerOperations((prev) => {
        const newSet = new Set(prev);
        newSet.delete(playerId);
        return newSet;
      });
    }, 100);
  };

  const handleFieldChange = (key: string, value: any) => {
    setFormData((prev) => ({ ...prev, [key]: value }));
  };

  const handlePlayerAdded = () => {
    setShowAddPlayer(false);
  };

  const validateForm = (): boolean => {
    return getPlayerCount() === 2; // Must have exactly 2 players
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    const { success, teamId, error } = await createPair(formData);

    if (success && teamId) {
      onSubmit(teamId);
    } else {
      throw new Error(error || 'Failed to create pair');
    }
  };

  const isFormValid = getPlayerCount() === 2;

  // Reset available players when modal closes
  useEffect(() => {
    if (!showSelectPlayer) {
      resetAvailablePlayers();
    }
  }, [showSelectPlayer, resetAvailablePlayers]);

  // Render player header similar to TeamSquad
  const renderPlayerHeader = () => {
    const isAddDisabled = !canAddMorePlayers();
    return (
      <View className="mb-4">
        <HStack className="items-center justify-between mb-3">
          <Text className="text-lg font-urbanistSemiBold text-typography-900">
            Players ({getPlayerCount()}/2)
          </Text>
          <HStack className="space-x-2 gap-3">
            <Button
              size="sm"
              variant="outline"
              className={`${isAddDisabled ? 'opacity-50' : ''}`}
              onPress={() => setShowSelectPlayer(true)}
              disabled={isAddDisabled}
            >
              <ButtonIcon as={UserPlusIcon} size="sm" />
              <ButtonText className="text-sm font-urbanistSemiBold">
                Select Player
              </ButtonText>
            </Button>
            <Button
              size="sm"
              className={`bg-primary-0 ${isAddDisabled ? 'opacity-50' : ''}`}
              onPress={() => setShowAddPlayer(true)}
              disabled={isAddDisabled}
            >
              <ButtonIcon as={PlusIcon} size="sm" />
              <ButtonText className="font-urbanistSemiBold">Add New</ButtonText>
            </Button>
          </HStack>
        </HStack>
      </View>
    );
  };

  const renderPlayerSlots = () => {
    const allPlayers = getAllPlayers();

    return (
      <VStack className="space-y-3 mb-6">
        {[0, 1].map((index) => {
          const player = allPlayers[index];
          const playerId =
            player && ('id' in player ? player.id : player.tempId);
          const isRemoving = playerId && playerOperations.has(playerId);

          return (
            <View
              key={index}
              className={`p-3 bg-white border-b ${
                player
                  ? 'border-outline-100'
                  : ' border-dashed border-outline-200'
              } ${isRemoving ? 'opacity-50' : ''}`}
            >
              {player ? (
                <HStack className="items-center justify-between">
                  <HStack className="items-center space-x-3 flex-1">
                    <LogoImage
                      width={40}
                      height={40}
                      borderRadius={100}
                      fallbackText={player.name}
                      fallBacktextClassName={'text-xl font-urbanistBold'}
                    />
                    <VStack className="flex-1 ml-3">
                      <View className="flex flex-row gap-1 items-start">
                        <Text className="text-base font-urbanistMedium text-typography-800">
                          {player.name}
                        </Text>
                        {'tempId' in player && (
                          <View className="w-1 h-1 rounded-full bg-primary-0" />
                        )}
                      </View>
                      {player.jersey_number && (
                        <Text className="text-xs text-typography-600 font-urbanist">
                          Jersey #{player.jersey_number}
                        </Text>
                      )}
                    </VStack>
                  </HStack>
                  <Pressable
                    onPress={() => {
                      triggerHapticFeedback();
                      handleRemovePlayer(playerId!);
                    }}
                    disabled={!!isRemoving}
                    className="w-6 h-6 items-center justify-center rounded-full border border-red-500"
                  >
                    <Icon as={XIcon} size="sm" className="text-red-500" />
                  </Pressable>
                </HStack>
              ) : (
                <HStack className="items-center justify-center py-3">
                  <Text className="text-typography-500 font-urbanist">
                    Player {index + 1} - Tap "Select Player" or "Add New" to add
                  </Text>
                </HStack>
              )}
            </View>
          );
        })}
      </VStack>
    );
  };

  return (
    <View className="flex-1">
      <ScrollView className="flex-1 bg-background-0" scrollEnabled={true}>
        <VStack className="p-4 space-y-4">
          {/* Player Header and Slots */}
          {renderPlayerHeader()}
          {renderPlayerSlots()}

          {/* Jersey Color and other fields */}
          {pairFields.map((field) => (
            <FormField
              key={field.key}
              type={field.type as FieldType}
              keyName={field.key}
              field={field}
              value={formData[field.key as keyof PairFormData]}
              onChange={handleFieldChange}
            />
          ))}
        </VStack>
      </ScrollView>

      {/* Sticky Bottom Button */}
      <View className="p-4 bg-background-0 border-t border-outline-100">
        <CTAButton
          title={submitButtonText}
          onPress={handleSubmit}
          isFormValid={isFormValid && !isLoading && !isCreatingPair}
          loading={isLoading || isCreatingPair}
        />
      </View>

      {/* Player Selection Modal */}
      <AsyncSelectWithSearch
        isOpen={showSelectPlayer}
        onClose={() => setShowSelectPlayer(false)}
        onSelect={handlePlayerSelectAndClose}
        options={availablePlayers}
        hasMore={hasMorePlayers}
        loadMore={loadMorePlayers}
        onSearchChange={handleSearchChange}
        placeholder="Search for players..."
        noDataFoundText="No players available"
        multiple={true}
        maxSelect={2 - getPlayerCount()}
        renderOption={(option, isSelected) => (
          <PlayerOptionRenderer
            option={option}
            isSelected={isSelected}
            showJerseyNumber={true}
          />
        )}
      />

      {/* Add New Player Modal */}
      <Actionsheet
        isOpen={showAddPlayer}
        onClose={() => setShowAddPlayer(false)}
      >
        <ActionsheetBackdrop />
        <ActionsheetContent className="max-h-[90%]">
          <ActionsheetDragIndicator />
          <AddPlayer
            tournament={tournament}
            onClose={handlePlayerAdded}
            onSubmit={handleAddLocalPlayer}
          />
        </ActionsheetContent>
      </Actionsheet>
    </View>
  );
};

export default memo(PairForm);
