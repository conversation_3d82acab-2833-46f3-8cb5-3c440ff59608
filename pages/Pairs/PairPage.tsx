import React, { memo } from 'react';
import { type Pair } from '@/types/pairs';
import { TabItem } from '@/types/tab';
import CollapsibleTabView from '@/components/CollapsibleTabView';
import PairOverview from './PairOverview';
import PairMatches from './PairMatches';
import PairStats from './PairStats';
import { teamTabState } from '@/atoms/teamTabs';
import { useRecoilState } from 'recoil';

const pairTabs: TabItem<Pair>[] = [
  {
    id: 'overview',
    name: 'Overview',
    render: (pair: Pair) => (
      <PairOverview pair={pair} tournamentId={pair.tournament_id} />
    ),
  },
  {
    id: 'matches',
    name: 'Matches',
    render: (pair: Pair) => (
      <PairMatches pair={pair} tournamentId={pair.tournament_id} />
    ),
  },
  {
    id: 'stats',
    name: 'Stats',
    render: (pair: Pair) => (
      <PairStats pair={pair} tournamentId={pair.tournament_id} />
    ),
  },
];

const PairPage: React.FC<{ pair: Pair }> = ({ pair }) => {
  const [tabState, setTabState] = useRecoilState(teamTabState);
  const selectedTab = tabState[pair.id] || 'overview';

  const handleTabChange = ({ tabName }: { tabName: string }) => {
    setTabState((prev) => ({
      ...prev,
      [pair.id]: tabName,
    }));
  };

  return (
    <CollapsibleTabView
      data={pair}
      tabs={pairTabs}
      lazy={true}
      initialTabName={selectedTab}
      onTabChange={handleTabChange}
    />
  );
};

export default memo(PairPage);
