import { useState, useCallback, useRef, useEffect } from 'react';
import {
  createPlayer,
  updatePlayer,
  fetchPlayers,
} from '@/services/playerService';
import { createTeam, updateTeam } from '@/services/teamsService';
import { type Player, type PlayerFormData } from '@/types/player';
import { type PairFormData } from '@/types/pairs';
import { type Tournament } from '@/pages/TournamentViewScreen/types';
import { type Option } from '@/components/k-components/AsyncSelectWithSearch';
import { toast } from '@/toast/toast';

interface LocalPlayer extends PlayerFormData {
  tempId: string; // Temporary ID for local management
}

interface UsePairPlayerManagementProps {
  tournament: Tournament;
  pairId?: string; // For edit mode
  initialPlayers?: Player[]; // Initial players for edit mode
}

export const usePairPlayerManagement = ({
  tournament,
  pairId,
  initialPlayers = [],
}: UsePairPlayerManagementProps) => {
  const [selectedPlayers, setSelectedPlayers] = useState<Player[]>([]);
  const [localPlayers, setLocalPlayers] = useState<LocalPlayer[]>([]);
  const [isCreatingPair, setIsCreatingPair] = useState(false);

  // Available players state (similar to useSquadManagement)
  const [availablePlayers, setAvailablePlayers] = useState<Option[]>([]);
  const [hasMorePlayers, setHasMorePlayers] = useState(false);
  const [loadingPlayers, setLoadingPlayers] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [consecutiveEmptyPages, setConsecutiveEmptyPages] = useState(0);

  const hasLoadedInitialData = useRef(false);
  const lastSearchQuery = useRef<string>('');

  // Initialize with existing players for edit mode
  useEffect(() => {
    if (pairId && initialPlayers.length > 0) {
      const limitedPlayers = initialPlayers.slice(0, 2); // Ensure max 2 players
      setSelectedPlayers(limitedPlayers);
    }
  }, [pairId, initialPlayers]);

  // Load available players for selection (exclude players already on teams)
  const loadAvailablePlayers = useCallback(
    async (searchQuery: string, isNewSearch = true, pageToLoad?: number) => {
      setLoadingPlayers(true);

      const targetPage = pageToLoad || (isNewSearch ? 1 : currentPage + 1);

      try {
        const { players: fetchedPlayers, error } = await fetchPlayers({
          tournamentId: tournament.id,
          search: searchQuery,
          page: targetPage,
          limit: 20,
        });

        if (error) {
          toast.error('Failed to load available players');
          return;
        }

        // Filter out players who already have a team_id
        // In edit mode, include current pair players (those with team_id matching pairId)
        const unassignedPlayers = (fetchedPlayers || []).filter(
          (player: Player) => {
            if (!player.team_id) return true; // Unassigned players
            if (pairId && player.team_id === pairId) return true; // Current pair players in edit mode
            return false; // All other assigned players
          }
        );

        const playerOptions: Option[] = unassignedPlayers.map(
          (player: Player) => ({
            label: player.name,
            value: player.id,
            player,
          })
        );

        const filterSelectedPlayers = playerOptions.filter(
          (option) =>
            !selectedPlayers.some((player) => player.id === option.value)
        );

        if (isNewSearch) {
          setAvailablePlayers(filterSelectedPlayers);
          setCurrentPage(1);
          setConsecutiveEmptyPages(0);
        } else {
          setAvailablePlayers((prev) => [...prev, ...filterSelectedPlayers]);
          setCurrentPage(targetPage);
        }

        const hasUnassignedPlayers = unassignedPlayers.length > 0;
        if (!isNewSearch) {
          if (hasUnassignedPlayers) {
            setConsecutiveEmptyPages(0);
          } else {
            setConsecutiveEmptyPages((prev) => prev + 1);
          }
        }

        // Determine if there are more pages to load
        const receivedFullPage = (fetchedPlayers || []).length === 20;
        const tooManyEmptyPages = consecutiveEmptyPages >= 3;
        const shouldContinue = receivedFullPage && !tooManyEmptyPages;
        setHasMorePlayers(shouldContinue);
      } catch (error) {
        toast.error('Something went wrong');
      } finally {
        setLoadingPlayers(false);
      }
    },
    [tournament.id, selectedPlayers]
  );

  const loadMorePlayers = useCallback(
    async (searchQuery: string, page: number) => {
      if (!loadingPlayers && hasMorePlayers) {
        await loadAvailablePlayers(searchQuery, false, page);
      }
    },
    [loadAvailablePlayers, loadingPlayers, hasMorePlayers]
  );

  const handleSearchChange = useCallback(
    (query: string) => {
      const isInitialEmptySearch =
        query === '' && !hasLoadedInitialData.current;
      const isUserClearingSearch =
        query === '' && lastSearchQuery.current !== '';
      const isNewSearch = query !== '';

      if (isInitialEmptySearch || isUserClearingSearch || isNewSearch) {
        loadAvailablePlayers(query, true);
        hasLoadedInitialData.current = true;
      }

      lastSearchQuery.current = query;
    },
    [loadAvailablePlayers]
  );

  // Add a locally created player
  const addLocalPlayer = (playerData: PlayerFormData) => {
    setLocalPlayers((prev) => {
      const currentTotal = selectedPlayers.length + prev.length;
      if (currentTotal >= 2) return prev; // Don't add if already at limit

      const localPlayer: LocalPlayer = {
        ...playerData,
        tempId: `temp_${Date.now()}_${Math.random()}`,
      };
      return [...prev, localPlayer];
    });
  };

  // Remove a locally created player
  const removeLocalPlayer = (tempId: string) => {
    setLocalPlayers((prev) => prev.filter((p) => p.tempId !== tempId));
  };

  // Add selected existing players
  const addSelectedPlayers = (players: Player[]) => {
    setSelectedPlayers((prev) => {
      const currentTotal = prev.length + localPlayers.length;
      const slotsLeft = 2 - currentTotal;

      if (slotsLeft <= 0) return prev; // No slots available

      const toAdd = players.slice(0, slotsLeft);
      return [...prev, ...toAdd];
    });
  };

  // Remove a selected player
  const removeSelectedPlayer = (playerId: string) => {
    setSelectedPlayers((prev) => prev.filter((p) => p.id !== playerId));
  };

  // Replace all selected players (useful for edit mode)
  const replaceSelectedPlayers = (players: Player[]) => {
    const limitedPlayers = players.slice(0, 2); // Ensure max 2 players
    setSelectedPlayers(limitedPlayers);
  };

  // Get all players (selected + local) for display
  const getAllPlayers = (): (Player | LocalPlayer)[] => {
    return [...selectedPlayers, ...localPlayers];
  };

  // Check if we can add more players
  const canAddMorePlayers = (): boolean => {
    return getAllPlayers().length < 2;
  };

  // Get total player count
  const getPlayerCount = (): number => {
    return getAllPlayers().length;
  };

  // Create pair with all the complex logic
  const createPair = async (
    pairFormData: PairFormData
  ): Promise<{ success: boolean; error?: string; teamId?: string }> => {
    setIsCreatingPair(true);

    try {
      let teamId: string;

      if (pairId) {
        // Edit mode: Update existing team
        const teamData = {
          jersey_color: pairFormData.jersey_color || undefined,
        };

        const { success: teamSuccess, error: teamError } = await updateTeam({
          teamId: pairId,
          team: teamData,
        });

        if (!teamSuccess) {
          return {
            success: false,
            error: teamError || 'Failed to update pair',
          };
        }

        teamId = pairId;
      } else {
        // Create mode: Create new team (pairs are teams in the backend)
        const teamData = {
          jersey_color: pairFormData.jersey_color || undefined,
        };

        const {
          success: teamSuccess,
          error: teamError,
          team,
        } = await createTeam({
          tournamentId: tournament.id,
          team: teamData,
        });

        if (!teamSuccess || !team) {
          return {
            success: false,
            error: teamError || 'Failed to create team',
          };
        }

        teamId = team.id;
      }

      // Step 2: Handle player updates in edit mode
      if (pairId) {
        // In edit mode, we need to handle both adding and removing players
        const currentPlayerIds = selectedPlayers.map((p) => p.id);
        const initialPlayerIds = initialPlayers.map((p) => p.id);

        // Remove players that are no longer in the pair
        const playersToRemove = initialPlayers.filter(
          (p) => !currentPlayerIds.includes(p.id)
        );
        for (const player of playersToRemove) {
          const { success: removeSuccess, error: removeError } =
            await updatePlayer(player.id, {
              team_id: null,
            });

          if (!removeSuccess) {
            console.error(
              `Failed to remove player ${player.name} from pair`,
              removeError
            );
          }
        }

        // Add new players to the pair
        const playersToAdd = selectedPlayers.filter(
          (p) => !initialPlayerIds.includes(p.id)
        );
        for (const player of playersToAdd) {
          const { success: addSuccess, error: addError } = await updatePlayer(
            player.id,
            {
              team_id: teamId,
            }
          );

          if (!addSuccess) {
            console.error(
              `Failed to add player ${player.name} to pair`,
              addError
            );
          }
        }
      } else {
        // Create mode: Add all selected players to the team
        for (const player of selectedPlayers) {
          const { success: updateSuccess, error: updateError } =
            await updatePlayer(player.id, {
              team_id: teamId,
            });

          if (!updateSuccess) {
            console.error(`Failed to add player ${player.name}`, updateError);
          }
        }
      }

      // Step 3: Handle local players - create them with team_id
      for (const localPlayer of localPlayers) {
        const { success: createSuccess, error: createError } =
          await createPlayer({
            tournament_id: tournament.id,
            name: localPlayer.name,
            team_id: teamId,
            jersey_number: localPlayer.jersey_number,
            email: localPlayer.email,
          });

        if (!createSuccess) {
          // If creating local players fails, we should clean up
          console.error(
            `Failed to add player ${localPlayer.name}`,
            createError
          );
        }
      }

      // Clear local state after successful creation (only in create mode)
      if (!pairId) {
        setSelectedPlayers([]);
        setLocalPlayers([]);
      }

      return { success: true, teamId };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to create pair',
      };
    } finally {
      setIsCreatingPair(false);
    }
  };

  // Reset all state
  const resetState = () => {
    setSelectedPlayers([]);
    setLocalPlayers([]);
  };

  // Reset available players state
  const resetAvailablePlayers = useCallback(() => {
    setAvailablePlayers([]);
    setHasMorePlayers(false);
    setCurrentPage(1);
    setConsecutiveEmptyPages(0);
    hasLoadedInitialData.current = false;
    lastSearchQuery.current = '';
  }, []);

  return {
    selectedPlayers,
    localPlayers,
    isCreatingPair,
    availablePlayers,
    hasMorePlayers,
    loadingPlayers,
    addLocalPlayer,
    removeLocalPlayer,
    addSelectedPlayers,
    removeSelectedPlayer,
    replaceSelectedPlayers,
    getAllPlayers,
    canAddMorePlayers,
    getPlayerCount,
    createPair,
    resetState,
    loadAvailablePlayers,
    loadMorePlayers,
    handleSearchChange,
    resetAvailablePlayers,
  };
};
