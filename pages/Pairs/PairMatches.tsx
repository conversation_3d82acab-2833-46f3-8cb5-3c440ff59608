import React from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { Icon } from '@/components/ui/icon';
import { CalendarIcon } from 'lucide-react-native';
import { type Pair } from '@/types/pairs';

interface PairMatchesProps {
  pair: Pair;
  tournamentId: string;
}

const PairMatches: React.FC<PairMatchesProps> = ({ pair, tournamentId }) => {
  return (
    <VStack className="px-4 space-y-6 pb-6 flex-1 gap-5 py-6">
      {/* Matches Overview */}
      <View className="bg-white rounded-lg p-4 shadow-sm border border-outline-100">
        <View className="flex-row items-center gap-2 mb-4">
          <Icon as={CalendarIcon} size="sm" className="text-primary-0" />
          <Text className="text-lg font-urbanistSemiBold text-typography-900">
            Match History
          </Text>
        </View>

        <Text className="text-base text-typography-600 text-center py-8 font-urbanist">
          No matches played yet. Match history will appear here once games begin.
        </Text>
      </View>

      {/* Upcoming Matches */}
      <View className="bg-white rounded-lg p-4 shadow-sm border border-outline-100">
        <View className="flex-row items-center gap-2 mb-4">
          <Icon as={CalendarIcon} size="sm" className="text-primary-0" />
          <Text className="text-lg font-urbanistSemiBold text-typography-900">
            Upcoming Matches
          </Text>
        </View>

        <Text className="text-base text-typography-600 text-center py-8 font-urbanist">
          No upcoming matches scheduled. Check back later for match updates.
        </Text>
      </View>
    </VStack>
  );
};

export default PairMatches;
