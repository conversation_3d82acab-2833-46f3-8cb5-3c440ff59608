import React from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { Icon } from '@/components/ui/icon';
import { BarChart3 } from 'lucide-react-native';
import { type Pair } from '@/types/pairs';

interface PairStatsProps {
  pair: Pair;
  tournamentId: string;
}

const PairStats: React.FC<PairStatsProps> = ({ pair, tournamentId }) => {
  return (
    <VStack className="px-4 space-y-6 pb-6 flex-1 gap-5 py-6">
      {/* Stats Overview */}
      <View className="bg-white rounded-lg p-4 shadow-sm border border-outline-100">
        <View className="flex-row items-center gap-2 mb-4">
          <Icon as={BarChart3} size="sm" className="text-primary-0" />
          <Text className="text-lg font-urbanistSemiBold text-typography-900">
            Performance Statistics
          </Text>
        </View>

        <Text className="text-base text-typography-600 text-center py-8 font-urbanist">
          Pair statistics will be displayed here once matches are played.
        </Text>
      </View>

      {/* Individual Player Stats */}
      <View className="bg-white rounded-lg p-4 shadow-sm border border-outline-100">
        <View className="flex-row items-center gap-2 mb-4">
          <Icon as={BarChart3} size="sm" className="text-primary-0" />
          <Text className="text-lg font-urbanistSemiBold text-typography-900">
            Individual Player Statistics
          </Text>
        </View>

        <Text className="text-base text-typography-600 text-center py-8 font-urbanist">
          Individual player performance data will be shown here after matches.
        </Text>
      </View>
    </VStack>
  );
};

export default PairStats;
