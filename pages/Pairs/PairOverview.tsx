import React, { memo, useMemo } from 'react';
import { View, Pressable } from 'react-native';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { UserIcon, PhoneIcon, MailIcon, Edit2Icon } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { type Pair } from '@/types/pairs';
import SCREENS from '@/constants/Screens';
import { isEmpty } from 'lodash';
import Jersey from '@/components/k-components/Jersey';
import LogoImage from '@/components/k-components/LogoImage';
import { type Player } from '@/types/player';
import { TeamMotto } from '../Teams/components/TeamMotto';
import ListInfoCard from '@/components/util-components/ListInfoCard';
import ActionRequired from '@/components/k-components/ActionRequired';
import { getInitialTeamInfoFormData } from '@/config/teamInfoFormConfig';
import { every } from 'lodash';

interface PairOverviewProps {
  pair: Pair;
  tournamentId: string;
}

const Player = memo(
  ({ player, jerseyColor }: { player: Player; jerseyColor?: string }) => {
    const router = useRouter();
    const handlePress = () => {
      router.push({
        pathname: SCREENS.PLAYER_VIEW,
        params: { 'player-id': player.id },
      });
    };

    return (
      <Pressable
        onPress={handlePress}
        className="flex-row items-center space-x-3"
      >
        <LogoImage
          width={35}
          height={35}
          borderRadius={60}
          fallbackText={player.name}
          fallBacktextClassName={'text-base font-urbanistBold'}
        />
        <VStack className="flex-1 ml-3">
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            className="text-lg font-urbanistSemiBold text-typography-800"
          >
            {player.name}
          </Text>
        </VStack>
        {jerseyColor && (
          <Jersey
            color={jerseyColor}
            number={player.jersey_number}
            width={35}
            height={35}
          />
        )}
      </Pressable>
    );
  }
);

const PairHeader = memo(({ pair }: { pair: Pair }) => {
  return (
    <View className="flex flex-col px-4 pt-4">
      <View className="flex flex-row items-center justify-between pt-4 px-3 w-full">
        <View className="flex flex-col justify-start flex-1 min-w-0 gap-5">
          {pair.player1 && (
            <Player player={pair.player1} jerseyColor={pair.jersey_color} />
          )}
          {pair.player2 && (
            <Player player={pair.player2} jerseyColor={pair.jersey_color} />
          )}
        </View>
      </View>
      <View className="flex-1 h-px bg-gray-300 mt-6 mx-3" />
    </View>
  );
});

const PairOverview: React.FC<PairOverviewProps> = ({ pair, tournamentId }) => {
  const router = useRouter();

  const handleSetTeamInfo = () => {
    router.push({
      pathname: SCREENS.TEAM_INFO,
      params: {
        'team-id': pair.id,
      },
    });
  };

  const infoItems = useMemo(() => {
    return [
      {
        icon: UserIcon,
        value: !isEmpty(pair?.owner_name)
          ? `Pair Owner: ${pair?.owner_name}`
          : '',
      },
      {
        icon: UserIcon,
        value: !isEmpty(pair?.coach_name) ? `Coach: ${pair?.coach_name}` : '',
      },
    ];
  }, [pair?.owner_name, pair?.coach_name]);

  const contactItems = useMemo(() => {
    return [
      {
        icon: PhoneIcon,
        value: pair?.team_phone || 'N/A',
        copyable: true,
      },
      {
        icon: MailIcon,
        value: pair?.team_email || 'N/A',
        copyable: true,
      },
    ];
  }, [pair?.team_phone, pair?.team_email]);

  const pairInfo = getInitialTeamInfoFormData(pair);
  const pairInfoMissing = every(pairInfo, isEmpty);

  return (
    <View className="flex-1">
      <PairHeader pair={pair} />
      <View className="h-full px-4 py-3">
        <TeamMotto motto={pair?.tagline} />
        <ActionRequired
          actions={[
            {
              id: 'set-pair-info',
              label: 'Set Pair Info',
              description:
                'Add important details like team owner, coach name, and contact details to finalize pair/team setup.',
              condition: pairInfoMissing,
              onPress: handleSetTeamInfo,
            },
          ]}
        />
        <View className="pt-6">
          <ListInfoCard title="Team Info" items={infoItems} />
        </View>
        <View className="pt-6">
          <ListInfoCard title="Contact" items={contactItems} />
        </View>
      </View>
    </View>
  );
};

export default memo(PairOverview);
