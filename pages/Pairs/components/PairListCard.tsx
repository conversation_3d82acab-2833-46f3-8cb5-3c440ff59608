import React, { memo, useCallback, useMemo } from 'react';
import { Pressable } from 'react-native';
import { useRouter } from 'expo-router';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Divider } from '@/components/ui/divider';
import LogoImage from '@/components/k-components/LogoImage';
import SCREENS from '@/constants/Screens';
import { type Player } from '@/types/player';

interface PlayerRowProps {
  player?: Player;
  fallbackText?: string;
}

export const PlayerRow: React.FC<PlayerRowProps> = memo(
  ({ player, fallbackText = '' }) => {
    const displayText = useMemo(() => {
      return player?.name || fallbackText;
    }, [player?.name, fallbackText]);

    return (
      <HStack className="items-center space-x-3 flex-1">
        <LogoImage
          width={30}
          height={30}
          borderRadius={20}
          fallbackText={player?.name || fallbackText}
          fallBacktextClassName={'text-sm font-urbanistBold'}
        />
        <VStack className="flex-1 ml-3">
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            className="text-base font-urbanistMedium text-typography-800"
          >
            {displayText}
          </Text>
        </VStack>
      </HStack>
    );
  }
);

interface PairListCardProps {
  pair: any;
  tournamentId: string;
}

const PairListCard: React.FC<PairListCardProps> = ({ pair, tournamentId }) => {
  const router = useRouter();

  const handlePress = useCallback(() => {
    router.push({
      pathname: SCREENS.PAIR_VIEW,
      params: {
        'pair-id': pair.id,
        'tournament-id': tournamentId,
      },
    });
  }, [router, pair.id, tournamentId]);

  // Use pre-fetched player data from the pair object
  const player1 = useMemo(() => pair.player1, [pair.player1]);
  const player2 = useMemo(() => pair.player2, [pair.player2]);

  return (
    <>
      <Pressable
        className="flex-row gap-2 items-center justify-between py-4 px-2 w-full"
        onPress={handlePress}
      >
        <PlayerRow player={player1} fallbackText="Player 1" />
        <PlayerRow player={player2} fallbackText="Player 2" />
      </Pressable>
      <Divider />
    </>
  );
};

export default memo(PairListCard);
