import React, { memo, useCallback, useMemo } from 'react';
import { View, Pressable } from 'react-native';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { PlusIcon } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import NoDataFound from '@/components/k-components/NoDataFound';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import EventsSection from '@/components/k-components/EventsSection';
import SCREENS from '@/constants/Screens';
import { useTournamentPairs } from './hooks/useTournamentTeams';
import { triggerHapticFeedback } from '@/utils';
import { type Tournament } from './types';
import { CTAButton } from '@/components/ui/primaryCTAbutton';
import PairListCard from '@/pages/Pairs/components/PairListCard';

interface TournamentPairsProps {
  tournament: Tournament;
}

const AddPairCTA = ({ onPress }: { onPress: () => void }) => (
  <Pressable
    onPress={() => {
      triggerHapticFeedback();
      onPress();
    }}
    className="flex-row items-center justify-start mt-4 px-2"
  >
    <View className="w-11 h-11 rounded-full bg-primary-0 items-center justify-center mr-3">
      <PlusIcon color="white" size={20} />
    </View>
    <Text className="text-primary-0 font-urbanistSemiBold">Add Pair</Text>
  </Pressable>
);

const TournamentPairs: React.FC<TournamentPairsProps> = ({ tournament }) => {
  const router = useRouter();
  const { pairs, totalCount, loading } = useTournamentPairs(tournament.id);

  const handleAddPair = useCallback(() => {
    router.push({
      pathname: SCREENS.PAIRS_CREATE,
      params: {
        'tournament-id': tournament.id,
      },
    });
  }, [router, tournament.id]);

  const extraParams = useMemo(
    () => ({
      tournamentId: tournament.id,
    }),
    [tournament.id]
  );

  const renderPairCard = useCallback(
    (pair: any) => <PairListCard pair={pair} tournamentId={tournament.id} />,
    [tournament.id]
  );

  if (loading) return <FullscreenLoader position="top" />;

  return (
    <VStack className="px-4 space-y-4 pb-6">
      {pairs.length === 0 ? (
        <NoDataFound
          title="No Pairs Added Yet"
          subtitle="Add pairs to get started with your tournament."
          action={
            <CTAButton
              title="Add Pair"
              lefticon={PlusIcon}
              onPress={handleAddPair}
            />
          }
        />
      ) : (
        <View>
          <EventsSection
            title={`Pairs (${totalCount})`}
            events={pairs}
            eventType="pairs"
            animated
            visibleCount={8}
            extraParams={extraParams}
            renderCard={renderPairCard}
          />
          <AddPairCTA onPress={handleAddPair} />
        </View>
      )}
    </VStack>
  );
};

export default memo(TournamentPairs);
