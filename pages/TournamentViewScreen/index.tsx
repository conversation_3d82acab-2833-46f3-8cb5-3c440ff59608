import React from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import LogoImage from '@/components/k-components/LogoImage';
import { Divider } from '@/components/ui/divider';
import { Icon } from '@/components/ui/icon';
import { Medal, MapPinIcon } from 'lucide-react-native';
import { Tournament } from '@/types/tournament';
import { TabItem } from '@/types/tab';
import { useRecoilState } from 'recoil';
import { tournamentTabState } from '@/atoms/tournamentTabs';
import CollapsibleTabView from '@/components/CollapsibleTabView';
import TournamentOverview from './TournamentOverview';
import TournamentTeams from './TournamentTeams';
import TournamentPlayers from './TournamentPlayers';
import TournamentPairs from './TournamentPairs';
import TournamentSchedules from './TournamentSchedules';
import { getSportLabel } from '@/utils/sports-utils';

interface TournamentViewScreenProps {
  tournament: Tournament;
}

const HEADER_HEIGHT = 200;

const MemoizedTournamentOverview = React.memo(TournamentOverview);
const MemoizedTournamentTeams = React.memo(TournamentTeams);
const MemoizedTournamentPairs = React.memo(TournamentPairs);
const MemoizedTournamentPlayers = React.memo(TournamentPlayers);
const MemoizedTournamentSchedules = React.memo(TournamentSchedules);

const tournamentTabs: TabItem<Tournament & { isTeamNameOptional?: boolean }>[] =
  [
    {
      id: 'overview',
      name: 'Overview',
      render: (tournament: Tournament) => (
        <MemoizedTournamentOverview tournament={tournament} />
      ),
    },
    {
      id: 'matches',
      name: 'Matches',
      render: (tournament: Tournament) => (
        <MemoizedTournamentSchedules tournament={tournament} />
      ),
    },
    {
      id: 'teams',
      name: 'Teams',
      render: (tournament: Tournament & { isTeamNameOptional?: boolean }) => (
        <MemoizedTournamentTeams tournament={tournament} />
      ),
    },
    {
      id: 'pairs',
      name: 'Pairs',
      render: (tournament: Tournament & { isTeamNameOptional?: boolean }) => (
        <MemoizedTournamentPairs tournament={tournament} />
      ),
    },
    {
      id: 'players',
      name: 'Players',
      render: (tournament: Tournament) => (
        <MemoizedTournamentPlayers tournament={tournament} />
      ),
    },
    {
      id: 'stats',
      name: 'Stats',
      render: () => (
        <Text className="text-base text-typography-600">Stats go here</Text>
      ),
    },
  ];

const TournamentHeader = ({ tournament }: { tournament: Tournament }) => (
  <View className="flex flex-col items-center justify-center bg-background-0 py-8">
    <LogoImage
      className="mb-2"
      logoUrl={tournament.logo_url}
      fallbackText={tournament.name}
    />
    <Text
      className="text-typography-700 font-urbanistBold text-2xl"
      numberOfLines={1}
      ellipsizeMode="tail"
    >
      {tournament.name}
    </Text>

    {(tournament.sport_type || tournament.venue) && (
      <>
        <Divider className="w-1/2 my-2" />
        <View className="flex flex-row items-center gap-1">
          {tournament.sport_type && (
            <View className="flex-row items-center gap-1">
              <Icon as={Medal} size="xs" className="text-typography-600" />
              <Text className="text-sm text-typography-600 font-urbanistMedium">
                {getSportLabel(tournament.sport_type)}
              </Text>
            </View>
          )}
          {tournament.sport_type && tournament.venue && (
            <View className="w-1 h-1 rounded-full bg-typography-600 mx-2 mt-1" />
          )}
          {tournament.venue && (
            <View className="flex-row items-center gap-1">
              <Icon as={MapPinIcon} size="xs" className="text-typography-600" />
              <Text className="text-sm text-typography-600 font-urbanistMedium">
                {tournament.venue}
              </Text>
            </View>
          )}
        </View>
      </>
    )}
  </View>
);

export default function TournamentViewScreen({
  tournament,
}: TournamentViewScreenProps) {
  const [tabState, setTabState] = useRecoilState(tournamentTabState);
  const selectedTab = tabState[tournament.id] || 'overview';

  const handleTabChange = ({ tabName }: { tabName: string }) => {
    setTabState((prev) => ({
      ...prev,
      [tournament.id]: tabName,
    }));
  };

  const format = tournament.tournament_rules?.game_format;

  const tabFilter = (
    tab: TabItem<Tournament & { isTeamNameOptional?: boolean }>
  ) => {
    // If singles: hide both Teams and Pairs
    if (format === 'singles') {
      return tab.id !== 'teams' && tab.id !== 'pairs';
    }
    // If doubles: hide Teams, show everything else (including Pairs)
    if (format === 'doubles') {
      return tab.id !== 'teams';
    }
    // Default (format undefined/unknown): hide only Pairs
    return tab.id !== 'pairs';
  };

  const tournamentData = {
    ...tournament,
  };

  const tabBarConfig = {
    scrollEnabled: true,
    minWidth: 110,
    paddingVertical: 8,
    paddingHorizontal: 20,
    tabStyle: {
      minWidth: 300,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
    },
    contentContainerStyle: { backgroundColor: '#fff' },
  };

  const renderHeader = React.useCallback(
    () => <TournamentHeader tournament={tournament} />,
    [tournament]
  );

  return (
    <CollapsibleTabView
      data={tournamentData}
      tabs={tournamentTabs}
      lazy={true}
      tabBarConfig={tabBarConfig}
      initialTabName={selectedTab}
      onTabChange={handleTabChange}
      renderHeader={renderHeader}
      headerHeight={HEADER_HEIGHT}
      tabFilter={tabFilter}
    />
  );
}
