import { useState, useEffect, useCallback } from 'react';
import { fetchTeams, fetchPairs } from '@/services/teamsService';
import { fetchPlayersForPairs } from '@/services/playerService';
import { toast } from '@/toast/toast';

type FetchFunction = typeof fetchTeams | typeof fetchPairs;

export const useTournamentEntities = (
  tournamentId: string,
  fetchFunction: FetchFunction
) => {
  const [entities, setEntities] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);

  const loadEntities = useCallback(async () => {
    setLoading(true);
    try {
      const {
        teams: fetchedEntities,
        count,
        error,
      } = await fetchFunction({ tournamentId });

      if (error) {
        toast.error(error || 'Something went wrong!');
        return;
      }

      setEntities(fetchedEntities || []);
      setTotalCount(count || 0);
    } catch (error) {
      toast.error('Something went wrong!');
    } finally {
      setLoading(false);
    }
  }, [tournamentId, fetchFunction]);

  useEffect(() => {
    loadEntities();
  }, [loadEntities]);

  return {
    entities,
    totalCount,
    loading,
    loadEntities,
  };
};

export const useTournamentTeams = (tournamentId: string) => {
  const result = useTournamentEntities(tournamentId, fetchTeams);

  return {
    teams: result.entities,
    totalCount: result.totalCount,
    loading: result.loading,
    loadTeams: result.loadEntities,
  };
};

export const useTournamentPairs = (tournamentId: string) => {
  const [pairs, setPairs] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);

  const loadPairs = useCallback(async () => {
    setLoading(true);
    try {
      const {
        teams: fetchedPairs,
        count,
        error: pairsError,
      } = await fetchPairs({ tournamentId });

      if (pairsError && __DEV__) {
        toast.error(pairsError || 'Something went wrong!');
        return;
      }

      const pairsList = fetchedPairs || [];
      setTotalCount(count || 0);

      if (pairsList.length === 0) {
        setPairs([]);
        return;
      }

      const pairIds = pairsList.map((pair: any) => pair.id);

      const { playersByPairId, error: playersError } =
        await fetchPlayersForPairs({
          tournamentId,
          pairIds,
        });

      if (playersError && __DEV__) {
        console.warn('Some players failed to load:', playersError);
      }

      const pairsWithPlayers = pairsList.map((pair: any) => {
        const players = playersByPairId[pair.id] || [];
        return {
          ...pair,
          player1: players[0] || null,
          player2: players[1] || null,
        };
      });

      setPairs(pairsWithPlayers);
    } catch (error) {
      toast.error('Something went wrong!');
      setPairs([]);
    } finally {
      setLoading(false);
    }
  }, [tournamentId]);

  useEffect(() => {
    loadPairs();
  }, [loadPairs]);

  return {
    pairs,
    totalCount,
    loading,
    loadPairs,
  };
};
