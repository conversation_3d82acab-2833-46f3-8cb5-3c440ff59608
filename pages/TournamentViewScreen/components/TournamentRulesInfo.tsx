import React from 'react';
import { Pressable, View } from 'react-native';
import ListInfoCard from '@/components/util-components/ListInfoCard';
import { sportRulesConfig } from '@/config/sportRulesConfig';
import { Tournament } from '@/types/tournament';
import { capitalizeFirst } from '@/utils';
import SCREENS from '@/constants/Screens';
import { useRouter } from 'expo-router';
import { ScreenMode } from '@/app/screens/tournament/[tournament-id]/rules';
import { type LucideIcon, PencilIcon } from 'lucide-react-native';
import { Icon } from '@/components/ui/icon';

interface Props {
  tournament: Tournament;
}

const TournamentRulesInfo = ({ tournament }: Props) => {
  const rulesConfig =
    sportRulesConfig[tournament.sport_type as keyof typeof sportRulesConfig];
  const rules = tournament.tournament_rules;

  const items =
    rulesConfig?.flatMap((rule) => {
      const value = rules?.[rule.key];
      if (value == null || !rule.view) return [];

      // For select or radio types, find the label from options
      let displayVal: string | number = value;
      if ((rule.type === 'select' || rule.type === 'radio') && rule.options) {
        const found = rule.options.find((opt) => opt.value === value);
        displayVal = found?.label ?? capitalizeFirst(value) ?? '';
      }

      return [
        {
          icon: rule.view.icon as LucideIcon,
          value: rule.view.displayText?.(displayVal) ?? String(displayVal),
        },
      ];
    }) ?? [];

  const router = useRouter();

  const handleEditPress = () => {
    router.push({
      pathname: SCREENS.TOURNAMENT_RULES,
      params: {
        'tournament-id': tournament.id,
        sport_type: tournament.sport_type,
        mode: ScreenMode.EDIT,
      },
    });
  };

  const headerRight = (
    <Pressable className="pl-3 py-1" onPress={handleEditPress}>
      <Icon as={PencilIcon} className="text-primary-0" />
    </Pressable>
  );

  if (items.length === 0) return null;

  return (
    <View className="mt-4">
      <ListInfoCard title="Rules" items={items} headerRight={headerRight} />
    </View>
  );
};

export default TournamentRulesInfo;
