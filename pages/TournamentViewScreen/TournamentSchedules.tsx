import React, { useCallback, useMemo } from 'react';
import { View } from 'react-native';
import { VStack } from '@/components/ui/vstack';
import { Tournament } from '@/types/tournament';
import NoDataFound from '@/components/k-components/NoDataFound';
import { PlusIcon } from 'lucide-react-native';
import { CTAButton } from '@/components/ui/primaryCTAbutton';
import { useRouter } from 'expo-router';
import SCREENS from '@/constants/Screens';
import RenderIf from '@/components/util-components/RenderIf';
import { isEmpty } from 'lodash';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import RoundMatchesList from '@/pages/TournamentMatches/components/RoundMatchesList';
import FilterSection from '@/pages/TournamentMatches/components/FilterSection';
import { useRoundMatches } from '@/hooks/useRoundMatches';
import { type Match } from '@/types/matches';
import { useMatchNavigation } from '@/pages/TournamentMatches/utils/matchNavigationUtils';

const TournamentSchedules = ({ tournament }: { tournament: Tournament }) => {
  const router = useRouter();
  const { navigateToMatch } = useMatchNavigation({
    tournamentId: tournament.id,
    tournamentEndDate: tournament.end_date,
  });

  const tournamentRoundNames = useMemo(() => {
    return tournament.metadata?.round_names || [];
  }, [tournament.metadata?.round_names]);

  const {
    roundMatches,
    loading,
    isEmpty: isMatchesEmpty,
  } = useRoundMatches({
    tournamentId: tournament.id,
    tournamentRoundNames,
    enabled: !isEmpty(tournament.format),
  });

  const handleCreateSchedule = useCallback(() => {
    router.push({
      pathname: SCREENS.SCHEDULE_CREATE,
      params: {
        'tournament-id': tournament.id,
      },
    });
  }, [router, tournament.id]);



  const handleCreateMatch = useCallback(() => {
    navigateToMatch('create');
  }, [navigateToMatch]);

  return (
    <View className="px-4 flex-1">
      <RenderIf condition={isEmpty(tournament.format)}>
        <NoDataFound
          title="No Schedules Yet"
          subtitle="Create schedules to build your tournament schedule."
          action={
            <CTAButton
              title="Create Schedule"
              lefticon={PlusIcon}
              onPress={handleCreateSchedule}
            />
          }
        />
      </RenderIf>

      <RenderIf condition={!isEmpty(tournament.format)}>
        <VStack className="space-y-4">
          <RenderIf condition={loading}>
            <FullscreenLoader position="top" />
          </RenderIf>

          <RenderIf condition={isMatchesEmpty && !loading}>
            <NoDataFound
              title="No Matches Yet"
              subtitle="Create matches to build your tournament schedule."
              action={
                <CTAButton
                  title="Create Match"
                  lefticon={PlusIcon}
                  onPress={handleCreateMatch}
                />
              }
            />
          </RenderIf>

          <RenderIf condition={!isMatchesEmpty && !loading}>
            <FilterSection tournament={tournament} />
            <RoundMatchesList
              roundMatches={roundMatches}
            />
          </RenderIf>
        </VStack>
      </RenderIf>
    </View>
  );
};

export default TournamentSchedules;
