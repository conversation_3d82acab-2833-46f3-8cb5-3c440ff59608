import React from 'react';
import { ScrollView, View } from 'react-native';
import { useRouter } from 'expo-router';
import { NavLayout } from '@/components/NavLayout';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import { StickyBottomButtons } from '@/components/k-components/StickyBottomButtons';
import { useTournamentById } from '@/hooks/useTournamentById';
import { useScheduleForm } from './hooks/useScheduleForm';
import ScheduleFormFields from './components/ScheduleFormFields';

interface ScheduleFormProps {
  tournamentId: string;
  mode?: string;
}

const ScheduleForm: React.FC<ScheduleFormProps> = ({ tournamentId, mode }) => {
  const router = useRouter();
  const { loading: tournamentLoading } = useTournamentById(tournamentId);

  const {
    form,
    errors,
    apiError,
    isSubmitting,
    loading,
    screenMode,
    title,
    submitButtonText,
    handleChange,
    handleSubmit,
  } = useScheduleForm({ tournamentId, mode });

  if (loading || tournamentLoading) {
    return <FullscreenLoader />;
  }

  return (
    <NavLayout title={title} noScroll>
      <View className="flex-1">
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 120 }}
        >
          <View className="px-4 py-4">
            <ScheduleFormFields
              form={form}
              errors={errors}
              screenMode={screenMode}
              onChange={handleChange}
              apiError={apiError}
            />
          </View>
        </ScrollView>

        <StickyBottomButtons
          primaryButton={{
            title: submitButtonText,
            onPress: handleSubmit,
            loading: isSubmitting,
            disabled: false,
          }}
          secondaryButton={{
            title: 'Cancel',
            onPress: () => router.back(),
            variant: 'cancel',
          }}
        />
      </View>
    </NavLayout>
  );
};

export default ScheduleForm;
