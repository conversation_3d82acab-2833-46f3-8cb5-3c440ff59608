import { 
  TOURNAMENT_FORMAT_LABELS, 
  TOURNAMENT_FORMATS,
  type TournamentFormat,
  type ScheduleFormData 
} from '@/config/scheduleFormConfig';

/**
 * Get the display label for a tournament format
 */
export const getTournamentFormatLabel = (format: string): string => {
  return (
    TOURNAMENT_FORMAT_LABELS[format as keyof typeof TOURNAMENT_FORMAT_LABELS] ||
    'Unknown Format'
  );
};

/**
 * Check if a tournament format is valid
 */
export const isValidTournamentFormat = (format: string): boolean => {
  return Object.values(TOURNAMENT_FORMATS).includes(format as TournamentFormat);
};

/**
 * Get all available tournament formats
 */
export const getAllTournamentFormats = () => {
  return Object.entries(TOURNAMENT_FORMAT_LABELS).map(([value, label]) => ({
    value,
    label,
  }));
};

/**
 * Format schedule data for display
 */
export const formatScheduleForDisplay = (schedule: ScheduleFormData) => {
  return {
    format: schedule.format ? getTournamentFormatLabel(schedule.format) : 'Not set',
  };
};

/**
 * Check if schedule form data is complete
 */
export const isScheduleFormComplete = (form: ScheduleFormData): boolean => {
  return Boolean(form.format && isValidTournamentFormat(form.format));
};

/**
 * Get schedule form validation errors
 */
export const getScheduleFormErrors = (form: ScheduleFormData): Record<string, string> => {
  const errors: Record<string, string> = {};

  if (!form.format) {
    errors.format = 'Tournament format is required';
  } else if (!isValidTournamentFormat(form.format)) {
    errors.format = 'Invalid tournament format selected';
  }

  return errors;
};

/**
 * Compare two schedule forms to detect changes
 */
export const hasScheduleFormChanged = (
  original: ScheduleFormData,
  current: ScheduleFormData
): boolean => {
  return original.format !== current.format;
};

/**
 * Get the default schedule form data
 */
export const getDefaultScheduleForm = (): ScheduleFormData => ({
  format: undefined,
});

/**
 * Sanitize schedule form data
 */
export const sanitizeScheduleForm = (form: ScheduleFormData): ScheduleFormData => ({
  format: form.format && isValidTournamentFormat(form.format) ? form.format : undefined,
});
