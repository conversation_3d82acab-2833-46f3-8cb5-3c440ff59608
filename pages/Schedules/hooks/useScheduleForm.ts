import { useState, useEffect, useMemo, useCallback } from 'react';
import { useRouter } from 'expo-router';
import { ScreenMode } from '@/components/k-components/EditableFormField';
import { toast } from '@/toast/toast';
import {
  type ScheduleFormData,
  getInitialScheduleFormData,
  validateTournamentFormat,
} from '@/config/scheduleFormConfig';
import {
  updateTournamentFields,
  fetchTournamentById,
} from '@/services/tournamentService';

interface UseScheduleFormProps {
  tournamentId: string;
  mode?: string;
}

interface UseScheduleFormReturn {
  form: ScheduleFormData;
  errors: Record<string, string>;
  apiError: string | null;
  isSubmitting: boolean;
  loading: boolean;
  screenMode: ScreenMode;
  title: string;
  submitButtonText: string;

  handleChange: (key: string, value: any) => void;
  handleSubmit: () => Promise<void>;
  validateForm: () => boolean;
}

export const useScheduleForm = ({
  tournamentId,
  mode,
}: UseScheduleFormProps): UseScheduleFormReturn => {
  const router = useRouter();

  const screenMode = useMemo(
    () => (mode === 'edit' ? ScreenMode.EDIT : ScreenMode.CREATE),
    [mode]
  );

  const [form, setForm] = useState<ScheduleFormData>(() =>
    getInitialScheduleFormData()
  );
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [apiError, setApiError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadTournamentData = async () => {
      if (screenMode === ScreenMode.EDIT && tournamentId) {
        try {
          const { success, data, error } = await fetchTournamentById(
            tournamentId
          );
          if (!success || !data) {
            toast.error(error || 'Failed to load tournament details');
            return;
          }
          setForm(getInitialScheduleFormData(data));
        } catch (error: any) {
          toast.error(error.message || 'Failed to load tournament details');
        }
      }
      setLoading(false);
    };

    loadTournamentData();
  }, [screenMode, tournamentId]);

  const handleChange = useCallback((key: string, value: any) => {
    setForm((prev) => ({ ...prev, [key]: value }));
    setErrors((prev) => ({ ...prev, [key]: '' }));
    setApiError(null);
  }, []);

  const validateForm = useCallback((): boolean => {
    const validationErrors: Record<string, string> = {};

    const formatError = validateTournamentFormat(form.format || '');
    if (formatError) {
      validationErrors.format = formatError;
    }

    setErrors(validationErrors);
    return Object.keys(validationErrors).length === 0;
  }, [form.format]);

  const handleSubmit = useCallback(async () => {
    setErrors({});
    setApiError(null);

    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);

      const { success, error } = await updateTournamentFields(tournamentId, {
        format: form.format,
      });

      if (!success) {
        toast.error(error || 'Failed to save schedule. Please try again.');
        return;
      }

      const successMessage =
        screenMode === ScreenMode.EDIT
          ? 'Schedule updated successfully'
          : 'Schedule created successfully';

      toast.success(successMessage);
      router.back();
    } catch (error: any) {
      toast.error(
        error.message || 'Failed to save schedule. Please try again.'
      );
    } finally {
      setIsSubmitting(false);
    }
  }, [validateForm, tournamentId, form.format, screenMode, router]);

  const title = useMemo(
    () =>
      screenMode === ScreenMode.EDIT ? 'Edit Schedule' : 'Create Schedule',
    [screenMode]
  );

  const submitButtonText = useMemo(
    () =>
      screenMode === ScreenMode.EDIT ? 'Update Schedule' : 'Create Schedule',
    [screenMode]
  );

  return {
    form,
    errors,
    apiError,
    isSubmitting,
    loading,
    screenMode,
    title,
    submitButtonText,
    handleChange,
    handleSubmit,
    validateForm,
  };
};
