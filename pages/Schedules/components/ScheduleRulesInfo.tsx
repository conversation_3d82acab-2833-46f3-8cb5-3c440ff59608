import React, { useMemo } from 'react';
import { Pressable, Text } from 'react-native';
import { Tournament } from '@/types/tournament';
import { SettingsIcon } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import SCREENS from '@/constants/Screens';
import { Icon } from '@/components/ui/icon';
import { getTournamentFormatLabel } from '../utils/scheduleUtils';
import { triggerHapticFeedback } from '@/utils';

const ScheduleRulesInfo = ({ tournament }: { tournament: Tournament }) => {
  const router = useRouter();

  const formatLabel = useMemo(() => {
    return getTournamentFormatLabel(tournament.format ?? '');
  }, [tournament.format]);

  const handleEditSchedule = () => {
    triggerHapticFeedback();
    router.push({
      pathname: SCREENS.SCHEDULE_CREATE,
      params: {
        'tournament-id': tournament.id,
        mode: 'edit',
      },
    });
  };

  return (
    <Pressable
      className="flex-row items-center border border-primary-0 bg-primary-0/10 active:bg-primary-0/10 rounded-md gap-2 px-2 py-1.5 w-auto"
      onPress={handleEditSchedule}
    >
      <Icon size="lg" as={SettingsIcon} className="text-primary-0" />
      <Text
        className="font-urbanistSemiBold text-primary-0"
        numberOfLines={1}
        ellipsizeMode="tail"
      >
        Schedule
      </Text>
    </Pressable>
  );
};

export default ScheduleRulesInfo;
