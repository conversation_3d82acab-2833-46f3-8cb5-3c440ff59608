import React from 'react';
import { VStack } from '@/components/ui/vstack';
import EditableFormField from '@/components/k-components/EditableFormField';
import { ErrorText } from '@/components/ui/errortext';
import {
  scheduleFormFields,
  type ScheduleFormData,
} from '@/config/scheduleFormConfig';
import { ScreenMode } from '@/components/k-components/EditableFormField';

interface ScheduleFormFieldsProps {
  form: ScheduleFormData;
  errors: Record<string, string>;
  screenMode: ScreenMode;
  onChange: (key: string, value: any) => void;
  apiError?: string | null;
  fields?: any[];
}

const ScheduleFormFields: React.FC<ScheduleFormFieldsProps> = ({
  form,
  errors,
  screenMode,
  onChange,
  apiError,
  fields = scheduleFormFields,
}) => {
  const renderFormField = (field: any) => (
    <EditableFormField
      key={field.key}
      field={field}
      screenMode={screenMode}
      value={form[field.key as keyof ScheduleFormData]}
      error={errors[field.key]}
      onChange={onChange}
    />
  );

  return (
    <VStack className="space-y-4 w-full">
      {fields.map(renderFormField)}
      {apiError && <ErrorText message={apiError} />}
    </VStack>
  );
};

export default ScheduleFormFields;
