import React, { memo, useCallback } from 'react';
import { View, Text, Pressable } from 'react-native';
import LogoImage from '@/components/k-components/LogoImage';
import { Divider } from '@/components/ui/divider';
import { useRouter } from 'expo-router';
import SCREENS from '@/constants/Screens';
import { type Player } from '@/types/player';

export const PlayerListCard = memo<{
  player: Player;
  isCaptain?: boolean;
}>(({ player, isCaptain = false }) => {
  const router = useRouter();

  const handlePress = useCallback(() => {
    router.push({
      pathname: SCREENS.PLAYER_VIEW,
      params: { 'player-id': player.id },
    });
  }, [router, player.id]);

  return (
    <>
      <Pressable
        className="flex-row items-center justify-between py-3 px-2"
        onPress={handlePress}
      >
        <View className="flex-row items-center space-x-3 gap-3 flex-1">
          <LogoImage
            width={40}
            height={40}
            borderRadius={100}
            fallbackText={player.name}
            fallBacktextClassName={'text-xl font-urbanistBold'}
          />
          <View className="flex-1 flex-row items-center gap-2">
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              className="text-base font-urbanistMedium text-typography-800"
            >
              {player.name}
            </Text>
            {isCaptain && (
              <View className="bg-primary-0/10 border border-primary-0 rounded-full w-5 h-5 items-center justify-center">
                <Text className="text-primary-0 text-xs font-urbanistExtraBold self-center pb-0.5">
                  C
                </Text>
              </View>
            )}
          </View>
        </View>

        {player.jersey_number && (
          <View className="flex flex-row items-center gap-1">
            <Text className="text-2xl text-typography-500 font-urbanistExtraBold ml-2 shrink-0">
              #
            </Text>
            <Text className="text-2xl text-typography-500 font-urbanistExtraBold shrink-0">
              {player.jersey_number}
            </Text>
          </View>
        )}
      </Pressable>
      <Divider />
    </>
  );
});
