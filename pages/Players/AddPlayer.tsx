import React, { useState } from 'react';
import { createPlayer } from '@/services/playerService';
import { type Tournament } from '@/pages/TournamentViewScreen/types';
import PlayerForm from '@/components/PlayerForm';
import { type PlayerFormData } from '@/types/player';

interface AddPlayerProps {
  tournament: Tournament;
  teamId?: string;
  onClose: () => void;
  onSubmit?: (data: PlayerFormData) => Promise<void>;
}

const AddPlayer: React.FC<AddPlayerProps> = ({
  tournament,
  teamId,
  onClose,
  onSubmit: externalSubmit,
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const defaultSubmit = async (formData: PlayerFormData) => {
    const { success, error: dbError } = await createPlayer({
      tournament_id: tournament.id,
      name: formData.name,
      ...(teamId ? { team_id: teamId } : {}),
      jersey_number: formData.jersey_number,
      email: formData.email,
    });

    if (!success) {
      throw new Error(dbError || 'Failed to add player, Try Again!');
    }
  };

  const handleFormSubmit = async (formData: PlayerFormData) => {
    setIsLoading(true);
    try {
      if (externalSubmit) {
        await externalSubmit(formData);
      } else {
        await defaultSubmit(formData);
      }
      onClose();
    } catch (err: any) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <PlayerForm
      onSubmit={handleFormSubmit}
      submitButtonText="Add Player"
      isLoading={isLoading}
    />
  );
};

export default AddPlayer;
