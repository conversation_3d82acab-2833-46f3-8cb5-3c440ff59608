import {
  PARTICIPANT_TYPES,
  type ParticipantType,
  type ParticipantDetails,
  type TeamParticipant,
  type PlayerParticipant,
  type PairParticipant,
  type TournamentWithRules,
} from '@/types/participants';

/**
 * Determines the participant type based on tournament rules
 * @param tournament Tournament object containing rules
 * @returns ParticipantType - 'player', 'pair', or 'team'
 */
export const getParticipantType = (
  tournament?: TournamentWithRules | null
): ParticipantType => {
  if (!tournament?.tournament_rules?.game_format) {
    return PARTICIPANT_TYPES.TEAM;
  }

  const gameFormat = tournament.tournament_rules.game_format;

  switch (gameFormat) {
    case 'singles':
      return PARTICIPANT_TYPES.PLAYER;
    case 'doubles':
      return PARTICIPANT_TYPES.PAIR;
    default:
      return PARTICIPANT_TYPES.TEAM;
  }
};

// Type guards for participant types
export const isTeamParticipant = (
  participant: ParticipantDetails
): participant is TeamParticipant => {
  return participant.type === 'team';
};

export const isPlayerParticipant = (
  participant: ParticipantDetails
): participant is PlayerParticipant => {
  return participant.type === 'player';
};

export const isPairParticipant = (
  participant: ParticipantDetails
): participant is PairParticipant => {
  return participant.type === 'pair';
};

/**
 * Gets the appropriate label for participant selection
 * @param participantType The type of participant
 * @returns Label string for the select component
 */
export const getParticipantLabel = (
  participantType: ParticipantType
): string => {
  switch (participantType) {
    case PARTICIPANT_TYPES.PLAYER:
      return 'Select Player';
    case PARTICIPANT_TYPES.PAIR:
      return 'Select Pair';
    case PARTICIPANT_TYPES.TEAM:
      return 'Select Team';
    default:
      return 'Select Team';
  }
};

/**
 * Gets the appropriate placeholder for participant search
 * @param participantType The type of participant
 * @returns Placeholder string for the search component
 */
export const getParticipantPlaceholder = (
  participantType: ParticipantType
): string => {
  switch (participantType) {
    case PARTICIPANT_TYPES.PLAYER:
      return 'Search for players...';
    case PARTICIPANT_TYPES.PAIR:
      return 'Search for pairs...';
    case PARTICIPANT_TYPES.TEAM:
      return 'Search for teams...';
    default:
      return 'Search for teams...';
  }
};

/**
 * Gets the appropriate no data message for participant selection
 * @param participantType The type of participant
 * @returns No data message string
 */
export const getParticipantNoDataMessage = (
  participantType: ParticipantType
): string => {
  switch (participantType) {
    case PARTICIPANT_TYPES.PLAYER:
      return 'No players found';
    case PARTICIPANT_TYPES.PAIR:
      return 'No pairs found';
    case PARTICIPANT_TYPES.TEAM:
      return 'No teams found';
    default:
      return 'No teams found';
  }
};
