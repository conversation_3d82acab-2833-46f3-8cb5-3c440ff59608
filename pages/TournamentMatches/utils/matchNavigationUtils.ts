import { useCallback } from 'react';
import { useRouter } from 'expo-router';
import SCREENS from '@/constants/Screens';
import { isTournamentCompleted } from '@/utils/tournament-utils';

export type MatchNavigationMode = 'create' | 'edit';

interface UseMatchNavigationProps {
  tournamentId: string;
  tournamentEndDate?: string;
}

interface UseMatchNavigationReturn {
  navigateToMatch: (mode: MatchNavigationMode, matchId?: string) => void;
}

/**
 * Custom hook for handling match navigation with create/edit modes
 * @param tournamentId - The tournament ID
 * @param tournamentEndDate - Optional tournament end date for completion check
 * @returns Object with navigateToMatch function
 */
export const useMatchNavigation = ({
  tournamentId,
  tournamentEndDate,
}: UseMatchNavigationProps): UseMatchNavigationReturn => {
  const router = useRouter();

  const navigateToMatch = useCallback(
    (mode: MatchNavigationMode, matchId?: string) => {
      // Check if tournament is completed for create mode
      if (mode === 'create' && tournamentEndDate && isTournamentCompleted(tournamentEndDate, true)) {
        return;
      }

      const params: Record<string, string> = {
        'tournament-id': tournamentId,
        mode,
      };

      // Add match ID for edit mode
      if (mode === 'edit' && matchId) {
        params['match-id'] = matchId;
      }

      router.push({
        pathname: SCREENS.CREATE_TOURNAMENT_MATCH,
        params,
      });
    },
    [router, tournamentId, tournamentEndDate]
  );

  return { navigateToMatch };
};

/**
 * Standalone utility function for match navigation
 * @param router - Expo router instance
 * @param tournamentId - The tournament ID
 * @param mode - Navigation mode (create or edit)
 * @param matchId - Optional match ID for edit mode
 * @param tournamentEndDate - Optional tournament end date for completion check
 */
export const navigateToMatchScreen = (
  router: any,
  tournamentId: string,
  mode: MatchNavigationMode,
  matchId?: string,
  tournamentEndDate?: string
) => {
  // Check if tournament is completed for create mode
  if (mode === 'create' && tournamentEndDate && isTournamentCompleted(tournamentEndDate, true)) {
    return;
  }

  const params: Record<string, string> = {
    'tournament-id': tournamentId,
    mode,
  };

  // Add match ID for edit mode
  if (mode === 'edit' && matchId) {
    params['match-id'] = matchId;
  }

  router.push({
    pathname: SCREENS.CREATE_TOURNAMENT_MATCH,
    params,
  });
};
