import React, { memo } from 'react';
import { View } from 'react-native';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import Shimmer from '../../../components/k-components/Shimmer';
import { PairParticipant } from '../../../types/participants';
import LogoImage from '../../../components/k-components/LogoImage';
import { getParticipantLogoFallback } from '../../../utils/tournament-match-utils';

interface PairParticipantDisplayProps {
  participant: PairParticipant | null;
}

const PlayerItemSkeleton: React.FC = () => (
  <HStack className="items-center space-x-2 gap-1">
    <Shimmer width={24} height={24} borderRadius={12} />
    <Shimmer width={60} height={14} borderRadius={7} />
  </HStack>
);

interface PlayerItemProps {
  playerName: string;
  participant: any;
}

const PlayerItem: React.FC<PlayerItemProps> = ({ playerName, participant }) => (
  <HStack className="items-center space-x-2 gap-1">
    <LogoImage
      width={24}
      height={24}
      borderRadius={24}
      fallbackText={getParticipantLogoFallback(participant, playerName)}
      fallBacktextClassName="text-xs text-center pb-[2px] font-urbanistBold"
    />
    <View className="flex-1 min-w-0">
      <Text
        numberOfLines={1}
        ellipsizeMode="tail"
        className="text-sm font-urbanistBold text-gray-900"
      >
        {playerName}
      </Text>
    </View>
  </HStack>
);

const PairParticipantDisplay: React.FC<PairParticipantDisplayProps> = ({
  participant,
}) => {
  if (!participant) {
    return (
      <VStack className="space-y-2 flex-1 gap-2">
        <PlayerItemSkeleton />
        <PlayerItemSkeleton />
      </VStack>
    );
  }

  return (
    <VStack className="space-y-2 flex-1 gap-2">
      <PlayerItem
        playerName={participant.player_1_name}
        participant={participant}
      />
      <PlayerItem
        playerName={participant.player_2_name}
        participant={participant}
      />
    </VStack>
  );
};

export default memo(PairParticipantDisplay);
