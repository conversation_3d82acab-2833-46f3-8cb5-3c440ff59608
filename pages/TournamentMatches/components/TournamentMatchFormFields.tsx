import React, { memo, useCallback, useMemo, useState } from 'react';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Card } from '@/components/ui/card';
import { View } from 'react-native';
import { ErrorText } from '@/components/ui/errortext';
import { AsyncSelectWithSearch } from '@/components/k-components/AsyncSelectWithSearch';
import { ParticipantOptionRenderer } from './ParticipantOptionRenderer';
import FormField, { FieldType } from '@/components/k-components/FormField';
import type { TournamentMatchFormData } from '@/config/tournamentMatchFormConfig';
import { createTournamentMatchFormFields } from '@/config/tournamentMatchFormConfig';
import type { ParticipantType } from '@/types/participants';
import {
  handleSelectFieldChange,
  getSelectFieldValue,
} from '@/utils/tournament-match-utils';
import type { Option } from '@/utils/option-utils';

interface TournamentMatchFormFieldsProps {
  form: TournamentMatchFormData;
  errors: Record<string, string>;
  participantType: ParticipantType;
  participantLabel: string;
  teamAOptions: Option[];
  teamBOptions: Option[];
  hasMoreTeamA: boolean;
  hasMoreTeamB: boolean;
  placeholder: string;
  noDataMessage: string;
  roundOptions: Option[];
  courtFieldOptions: Option[];
  onChange: (key: string, value: any) => void;
  onLoadMoreTeamA: (searchQuery: string, page: number) => Promise<void>;
  onLoadMoreTeamB: (searchQuery: string, page: number) => Promise<void>;
  onRoundCreate: (newRoundName: string) => Promise<Option>;
  onCourtFieldCreate: (newCourtFieldName: string) => Promise<Option>;
  tournamentStartDate?: string | null;
  tournamentEndDate?: string | null;
  apiError?: string | null;
}

// Reusable participant selection component
const ParticipantSelectionField = memo<{
  label: string;
  value: Option | null | undefined;
  participantLabel: string;
  participantName: string;
  error?: string;
  onPress: () => void;
  onMarkTBD: () => void;
  renderParticipantOption: (
    option: Option,
    isSelected: boolean
  ) => React.ReactNode;
}>(
  ({
    label,
    value,
    participantLabel,
    participantName,
    error,
    onPress,
    onMarkTBD,
    renderParticipantOption,
  }) => (
    <VStack className="space-y-2">
      <Text className="text-base font-urbanistSemiBold text-typography-800">
        {label}
      </Text>
      <View className="border border-outline-200 rounded-lg p-4 bg-background-0">
        {value ? (
          <View className="flex-row items-center justify-between">
            {renderParticipantOption(value, false)}
            <Text
              className="text-typography-500 font-urbanistMedium"
              onPress={onPress}
            >
              Change
            </Text>
          </View>
        ) : value === null ? (
          <View className="flex-row items-center justify-between">
            <Text className="text-typography-900 font-urbanistMedium">TBD</Text>
            <Text
              className="text-typography-500 font-urbanistMedium"
              onPress={onPress}
            >
              Change
            </Text>
          </View>
        ) : (
          <Text
            className="text-typography-500 font-urbanistMedium"
            onPress={onPress}
          >
            Tap to select {participantLabel.toLowerCase()}
          </Text>
        )}
      </View>
      {error && <ErrorText message={error} />}

      {/* TBD Option */}
      <View className="mt-2">
        <Text className="text-typography-600 font-urbanist text-xs">
          {participantName} yet to be decided?{' '}
          <Text
            className="text-primary-0 text-sm font-urbanistMedium underline underline-offset-8"
            onPress={onMarkTBD}
          >
            Click here
          </Text>
        </Text>
      </View>
    </VStack>
  )
);

ParticipantSelectionField.displayName = 'ParticipantSelectionField';

const TournamentMatchFormFields = memo<TournamentMatchFormFieldsProps>(
  ({
    form,
    errors,
    participantType,
    participantLabel,
    teamAOptions,
    teamBOptions,
    hasMoreTeamA,
    hasMoreTeamB,
    placeholder,
    noDataMessage,
    roundOptions,
    courtFieldOptions,
    onChange,
    onLoadMoreTeamA,
    onLoadMoreTeamB,
    onRoundCreate,
    onCourtFieldCreate,
    tournamentStartDate,
    tournamentEndDate,
    apiError,
  }) => {
    const [showTeamASelect, setShowTeamASelect] = useState(false);
    const [showTeamBSelect, setShowTeamBSelect] = useState(false);

    const formFields = useMemo(
      () =>
        createTournamentMatchFormFields(
          roundOptions,
          courtFieldOptions,
          onRoundCreate,
          onCourtFieldCreate,
          tournamentStartDate,
          tournamentEndDate
        ),
      [
        roundOptions,
        courtFieldOptions,
        onRoundCreate,
        onCourtFieldCreate,
        tournamentStartDate,
        tournamentEndDate,
      ]
    );

    const participantName = useMemo(
      () => participantLabel.replace('Select ', ''),
      [participantLabel]
    );

    const handleTeamASearchChange = useCallback(
      (query: string) => onLoadMoreTeamA(query, 1),
      [onLoadMoreTeamA]
    );

    const handleTeamBSearchChange = useCallback(
      (query: string) => onLoadMoreTeamB(query, 1),
      [onLoadMoreTeamB]
    );

    const renderParticipantOption = useCallback(
      (option: Option, isSelected: boolean) => (
        <ParticipantOptionRenderer
          option={option}
          isSelected={isSelected}
          participantType={participantType}
        />
      ),
      [participantType]
    );

    const handleTeamASelect = useCallback(
      (selected: Option | Option[] | null) => {
        const selectedOption = Array.isArray(selected)
          ? selected[0] || null
          : selected;
        onChange('teamA', selectedOption);
        setShowTeamASelect(false);
      },
      [onChange]
    );

    const handleTeamBSelect = useCallback(
      (selected: Option | Option[] | null) => {
        const selectedOption = Array.isArray(selected)
          ? selected[0] || null
          : selected;
        onChange('teamB', selectedOption);
        setShowTeamBSelect(false);
      },
      [onChange]
    );

    const getFieldValue = useCallback(
      (field: any) => getSelectFieldValue(field.key, form),
      [form]
    );

    const handleFieldChange = useCallback(
      (key: string, value: any) => {
        switch (key) {
          case 'scheduled_date':
            onChange(key, value || null);
            break;
          case 'stage':
            handleSelectFieldChange(key, value, roundOptions, onChange);
            break;
          case 'court_field_number':
            handleSelectFieldChange(key, value, courtFieldOptions, onChange);
            break;
          default:
            onChange(key, value);
        }
      },
      [onChange, roundOptions, courtFieldOptions]
    );

    const handleTeamATBD = useCallback(
      () => onChange('teamA', null),
      [onChange]
    );

    const handleTeamBTBD = useCallback(
      () => onChange('teamB', null),
      [onChange]
    );

    return (
      <VStack space="lg" className="flex-1">
        <Card
          size="md"
          variant="filled"
          className="rounded-xl bg-primary-0/5 border border-primary-0"
        >
          {/* Team A Selection */}
          <ParticipantSelectionField
            label={`${participantLabel} A`}
            value={form.teamA}
            participantLabel={participantLabel}
            participantName={participantName}
            error={errors.teamA}
            onPress={() => setShowTeamASelect(true)}
            onMarkTBD={handleTeamATBD}
            renderParticipantOption={renderParticipantOption}
          />

          {/* VS Divider */}
          <View className="items-center gap-2 flex-row mt-4 flex-1 justify-center">
            <View className="w-10 border-t border-typography-400" />
            <Text className="text-typography-700 font-urbanistBlackItalic text-base">
              VS
            </Text>
            <View className="w-10 border-t border-typography-400" />
          </View>

          {/* Team B Selection */}
          <ParticipantSelectionField
            label={`${participantLabel} B`}
            value={form.teamB}
            participantLabel={participantLabel}
            participantName={participantName}
            error={errors.teamB}
            onPress={() => setShowTeamBSelect(true)}
            onMarkTBD={handleTeamBTBD}
            renderParticipantOption={renderParticipantOption}
          />
        </Card>

        {/* Additional Form Fields */}
        {formFields.map((field) => (
          <FormField
            key={field.key}
            type={field.type as FieldType}
            keyName={field.key}
            field={field}
            value={getFieldValue(field)}
            error={errors[field.key]}
            onChange={handleFieldChange}
          />
        ))}

        {/* API Error */}
        {apiError && <ErrorText message={apiError} />}

        {/* Team A Selection Modal */}
        <AsyncSelectWithSearch
          isOpen={showTeamASelect}
          onClose={() => setShowTeamASelect(false)}
          onSelect={handleTeamASelect}
          selectedValue={form.teamA || undefined}
          options={teamAOptions}
          hasMore={hasMoreTeamA}
          loadMore={onLoadMoreTeamA}
          onSearchChange={handleTeamASearchChange}
          placeholder={placeholder}
          noDataFoundText={noDataMessage}
          renderOption={renderParticipantOption}
          multiple={false}
        />

        {/* Team B Selection Modal */}
        <AsyncSelectWithSearch
          isOpen={showTeamBSelect}
          onClose={() => setShowTeamBSelect(false)}
          onSelect={handleTeamBSelect}
          selectedValue={form.teamB || undefined}
          options={teamBOptions}
          hasMore={hasMoreTeamB}
          loadMore={onLoadMoreTeamB}
          onSearchChange={handleTeamBSearchChange}
          placeholder={placeholder}
          noDataFoundText={noDataMessage}
          renderOption={renderParticipantOption}
          multiple={false}
        />
      </VStack>
    );
  }
);

TournamentMatchFormFields.displayName = 'TournamentMatchFormFields';

export default TournamentMatchFormFields;
