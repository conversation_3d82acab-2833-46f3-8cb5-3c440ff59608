import React from "react";
import { <PERSON>, ScrollView } from "react-native";
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
} from "@/components/ui/actionsheet";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Divider } from "@/components/ui/divider";
import { Match } from "../../../types/matches";
import { ParticipantDetails } from "../../../types/participants";
import ParticipantDisplay from "./ParticipantDisplay";
import RenderIf from "@/components/util-components/RenderIf";
import { getMatchStatusText } from "../utils/matchUtils";
import MatchActions from "@/components/match-components/MatchActions";
import { MatchActionHandlers } from "@/utils/matchActions";
import { But<PERSON>, ButtonText } from "@/components/ui/button";

export interface MatchDetailsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  match: Match;
  participant1Details?: ParticipantDetails | null;
  participant2Details?: ParticipantDetails | null;
  actionHandlers?: MatchActionHandlers;
}

const MatchDetailsDialog: React.FC<MatchDetailsDialogProps> = ({
  isOpen,
  onClose,
  match,
  participant1Details,
  participant2Details,
  actionHandlers,
}) => {
  const matchStatusText = getMatchStatusText(
    match.scheduled_date,
    match.status
  );

  return (
    <Actionsheet isOpen={isOpen} onClose={onClose}>
      <ActionsheetBackdrop />
      <ActionsheetContent>
        <ActionsheetDragIndicatorWrapper>
          <ActionsheetDragIndicator />
        </ActionsheetDragIndicatorWrapper>

        <ScrollView
          className="w-full mt-3"
          showsVerticalScrollIndicator={false}
        >
          <VStack className="">
            {/* Participants Section */}
            <VStack className="space-y-4 p-4">
              <HStack className="items-center justify-between">
                <View className="flex-1 max-w-[45%]">
                  <ParticipantDisplay
                    participantId={match.participant_1_id}
                    participantType={match.participant_type}
                    participantName={match.participant_1_name}
                    fallbackName="TBD"
                    preloadedParticipant={participant1Details}
                  />
                </View>

                <View className="px-4">
                  <Text className="text-xl font-urbanistExtraBold text-gray-400 tracking-widest">
                    VS
                  </Text>
                </View>

                <View className="flex-1 max-w-[45%]">
                  <ParticipantDisplay
                    participantId={match.participant_2_id}
                    participantType={match.participant_type}
                    participantName={match.participant_2_name}
                    fallbackName="TBD"
                    preloadedParticipant={participant2Details}
                  />
                </View>
              </HStack>
            </VStack>
            <Text className="text-sm font-urbanistMedium text-gray-800 self-center mb-2">
              {matchStatusText}
            </Text>
            <RenderIf condition={!!match?.court_field_number}>
              <Divider className="w-1/2 items-center self-center" />
              <Text className="text-sm font-urbanistMedium text-gray-800 self-center my-2 mb-7">
                {match.court_field_number}
              </Text>
            </RenderIf>

            <View className="border-t border-gray-300 border-dashed" />
            <VStack className="space-y-4 p-4 gap-2">
              {actionHandlers && (
                <MatchActions
                  scheduledDate={match.scheduled_date}
                  currentStatus={match.status}
                  hasResults={
                    !!(
                      match.participant_1_score ||
                      match.participant_2_score ||
                      match.detailed_results
                    )
                  }
                  handlers={actionHandlers}
                >
                  {(actions, handleActionPress, loadingAction) => (
                    <VStack className="space-y-3">
                      {actions.map((action) => (
                        <Button
                          key={action.id}
                          onPress={() => handleActionPress(action)}
                          variant={
                            action.type === "secondary" ? "outline" : "solid"
                          }
                          action={
                            action.type === "danger"
                              ? "negative"
                              : action.type === "secondary"
                              ? "secondary"
                              : "primary"
                          }
                          isDisabled={loadingAction !== null}
                          className="w-full"
                        >
                          <ButtonText>{action.title}</ButtonText>
                        </Button>
                      ))}
                    </VStack>
                  )}
                </MatchActions>
              )}
            </VStack>
          </VStack>
        </ScrollView>
      </ActionsheetContent>
    </Actionsheet>
  );
};

export default MatchDetailsDialog;
