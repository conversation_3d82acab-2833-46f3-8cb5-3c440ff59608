import React, { memo } from "react";
import {
  ParticipantType,
  TeamParticipant,
  PlayerParticipant,
  PairParticipant,
  ParticipantDetails,
} from "../../../types/participants";
import { useParticipantDetails } from "../../../hooks/useParticipantDetails";
import TeamParticipantDisplay from "./TeamParticipantDisplay";
import PlayerParticipantDisplay from "./PlayerParticipantDisplay";
import PairParticipantDisplay from "./PairParticipantDisplay";

interface ParticipantDisplayProps {
  participantId: string | null;
  participantType: ParticipantType;
  participantName?: string | null;
  fallbackName: string;
  preloadedParticipant?: ParticipantDetails | null;
}

const ParticipantDisplay: React.FC<ParticipantDisplayProps> = ({
  participantId,
  participantType,
  participantName,
  fallbackName,
  preloadedParticipant,
}) => {
  const { participant: fetchedParticipant } = useParticipantDetails({
    participantId: preloadedParticipant ? null : participantId, // Skip fetching if preloaded data is available
    participantType,
    participantName: participantName || fallbackName,
  });

  // Use preloaded participant if available, otherwise use fetched participant
  const participant = preloadedParticipant || fetchedParticipant;

  switch (participantType) {
    case "team":
      return (
        <TeamParticipantDisplay
          participant={participant as TeamParticipant | null}
        />
      );
    case "player":
      return (
        <PlayerParticipantDisplay
          participant={participant as PlayerParticipant | null}
        />
      );
    case "pair":
      return (
        <PairParticipantDisplay
          participant={participant as PairParticipant | null}
        />
      );
    default:
      return null;
  }
};

export default memo(ParticipantDisplay);
