import React, { memo } from "react";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import LogoImage from "../../../components/k-components/LogoImage";
import Shimmer from "../../../components/k-components/Shimmer";
import { TeamParticipant } from "../../../types/participants";
import { getParticipantLogoFallback } from "../../../utils/tournament-match-utils";

interface TeamParticipantDisplayProps {
  participant: TeamParticipant | null;
}

const TeamParticipantDisplay: React.FC<TeamParticipantDisplayProps> = ({
  participant,
}) => {
  if (!participant) {
    return (
      <VStack className="items-center space-y-2 flex-1 gap-1">
        <LogoImage
          width={53}
          height={53}
          borderRadius={100}
          fallbackText=""
          fallBacktextClassName="text-lg font-urbanistBold"
        />
        <VStack className="items-center space-y-1 gap-1">
          <Shimmer width={60} height={15} borderRadius={8} />
          <Shimmer width={40} height={12} borderRadius={6} />
        </VStack>
      </VStack>
    );
  }

  return (
    <VStack className="items-center space-y-2 flex-1 gap-1">
      <LogoImage
        width={53}
        height={53}
        borderRadius={100}
        logoUrl={participant.logo_url}
        fallbackText={getParticipantLogoFallback(participant, participant.name)}
        fallBacktextClassName="text-lg font-urbanistBold"
      />
      <VStack className="items-center space-y-1">
        <Text
          numberOfLines={1}
          ellipsizeMode="tail"
          className="text-sm font-urbanistBold text-gray-900 text-center"
        >
          {participant.short_name || participant.name}
        </Text>
        {participant.short_name &&
          participant.short_name !== participant.name && (
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              className="text-xs text-gray-600 font-urbanistMedium text-center"
            >
              {participant.name}
            </Text>
          )}
      </VStack>
    </VStack>
  );
};

export default memo(TeamParticipantDisplay);
