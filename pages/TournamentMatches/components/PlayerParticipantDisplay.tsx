import React, { memo } from 'react';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import LogoImage from '../../../components/k-components/LogoImage';
import Shimmer from '../../../components/k-components/Shimmer';
import { PlayerParticipant } from '../../../types/participants';
import { getParticipantLogoFallback } from '../../../utils/tournament-match-utils';

interface PlayerParticipantDisplayProps {
  participant: PlayerParticipant | null;
}

const PlayerParticipantDisplay: React.FC<PlayerParticipantDisplayProps> = ({
  participant,
}) => {
  if (!participant) {
    return (
      <VStack className="items-center space-y-2 flex-1 gap-1">
        <LogoImage
          width={48}
          height={48}
          borderRadius={24}
          fallbackText="?"
          fallBacktextClassName="text-base font-urbanistBold"
        />
        <VStack className="items-center space-y-1 gap-1">
          <Shimmer width={60} height={16} borderRadius={8} />
          <Shimmer width={40} height={12} borderRadius={6} />
        </VStack>
      </VStack>
    );
  }

  return (
    <VStack className="items-center space-y-2 flex-1 gap-1">
      <LogoImage
        width={48}
        height={48}
        borderRadius={24}
        fallbackText={getParticipantLogoFallback(participant, participant.name)}
        fallBacktextClassName="text-base font-urbanistBold"
      />
      <VStack className="items-center space-y-1">
        <Text
          numberOfLines={1}
          ellipsizeMode="tail"
          className="text-sm font-urbanistBold text-gray-900 text-center"
        >
          {participant.name}
        </Text>
        {(participant.jersey_number || participant.position) && (
          <Text
            numberOfLines={1}
            ellipsizeMode="tail"
            className="text-xs text-gray-600 font-urbanistMedium text-center"
          >
            {participant.jersey_number && `#${participant.jersey_number}`}
            {participant.jersey_number && participant.position && ' • '}
            {participant.position}
          </Text>
        )}
      </VStack>
    </VStack>
  );
};

export default memo(PlayerParticipantDisplay);
