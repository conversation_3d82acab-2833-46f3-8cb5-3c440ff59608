import React from 'react';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import LogoImage from '@/components/k-components/LogoImage';
import { type Option } from '@/components/k-components/AsyncSelectWithSearch';
import { PARTICIPANT_TYPES, type ParticipantType } from '@/types/participants';

interface ParticipantOptionRendererProps {
  option: Option;
  isSelected: boolean;
  participantType: ParticipantType;
}

export const ParticipantOptionRenderer: React.FC<
  ParticipantOptionRendererProps
> = ({ option, isSelected, participantType }) => {
  const renderTeamOption = () => (
    <HStack className="flex-1 items-center space-x-3 gap-3">
      <LogoImage
        width={40}
        height={40}
        borderRadius={20}
        logoUrl={option.team?.logo_url}
        fallbackText={option.label}
        fallBacktextClassName="text-sm font-urbanistBold"
      />
      <VStack className="flex-1">
        <Text
          numberOfLines={1}
          ellipsizeMode="tail"
          className={`text-base ${
            isSelected
              ? 'font-urbanistBold text-primary-0'
              : 'font-urbanistMedium'
          }`}
        >
          {option.label}
        </Text>
        {option.team?.short_name && (
          <Text
            className={`text-xs text-typography-600 ${
              isSelected
                ? 'font-urbanistMedium text-primary-0'
                : 'font-urbanist'
            }`}
          >
            {option.team.short_name}
          </Text>
        )}
      </VStack>
    </HStack>
  );

  const renderPlayerOption = () => (
    <HStack className="flex-1 items-center space-x-3 gap-3">
      <LogoImage
        width={40}
        height={40}
        borderRadius={100}
        fallbackText={option.label}
        fallBacktextClassName="text-sm font-urbanistBold"
      />
      <VStack className="flex-1">
        <Text
          numberOfLines={1}
          ellipsizeMode="tail"
          className={`text-base ${
            isSelected
              ? 'font-urbanistBold text-primary-0'
              : 'font-urbanistMedium'
          }`}
        >
          {option.label}
        </Text>
        {option.player?.jersey_number && (
          <Text
            className={`text-xs text-typography-600 ${
              isSelected
                ? 'font-urbanistMedium text-primary-0'
                : 'font-urbanist'
            }`}
          >
            Jersey #{option.player.jersey_number}
          </Text>
        )}
      </VStack>
    </HStack>
  );

  const renderPairOption = () => (
    <HStack className="flex-1 items-center space-x-3">
      <VStack className="flex-1 py-2">
        {option.players && option.players.length === 2 && (
          <Text
            className={`text-base text-typography-900 ${
              isSelected
                ? 'font-urbanistBold text-primary-0'
                : 'font-urbanistMedium'
            }`}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {option.players[0]?.name} / {option.players[1]?.name}
          </Text>
        )}
      </VStack>
    </HStack>
  );

  switch (participantType) {
    case PARTICIPANT_TYPES.PLAYER:
      return renderPlayerOption();
    case PARTICIPANT_TYPES.PAIR:
      return renderPairOption();
    case PARTICIPANT_TYPES.TEAM:
      return renderTeamOption();
    default:
      return renderTeamOption();
  }
};
