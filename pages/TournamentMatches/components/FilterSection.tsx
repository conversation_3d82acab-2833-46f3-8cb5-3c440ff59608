import React, { useCallback } from 'react';
import { Pressable, View } from 'react-native';
import { Tournament } from '@/types/tournament';
import { PlusIcon, SlidersHorizontalIcon } from 'lucide-react-native';
import { Icon } from '@/components/ui/icon';
import { Button, ButtonText } from '@/components/ui/button';
import { toast } from '@/toast/toast';
import { useMatchNavigation } from '../utils/matchNavigationUtils';
import ScheduleRulesInfo from '../../Schedules/components/ScheduleRulesInfo';

interface FilterSectionProps {
  tournament: Tournament;
}

const FilterSection = ({ tournament }: FilterSectionProps) => {
  const { navigateToMatch } = useMatchNavigation({
    tournamentId: tournament.id,
    tournamentEndDate: tournament.end_date,
  });

  const handleCreateMatch = useCallback(() => {
    navigateToMatch('create');
  }, [navigateToMatch]);

  return (
    <View className="flex flex-row justify-between items-center py-3">
      <View className="flex flex-row items-center gap-2">
        <Pressable
          className="flex-row items-center justify-between border border-primary-0 bg-primary-0/10 rounded-md px-3 py-1.5 gap-2"
          onPress={() => toast.info('Filters coming soon!')}
        >
          <Icon
            as={SlidersHorizontalIcon}
            size="lg"
            className="text-primary-0"
          />
        </Pressable>
        <ScheduleRulesInfo tournament={tournament} />
      </View>

      <Button
        size="sm"
        variant="solid"
        onPress={handleCreateMatch}
        className="bg-primary-0"
      >
        <Icon as={PlusIcon} size="lg" className="text-white" />
        <ButtonText className="font-urbanistSemiBold text-white text-md">
          Create Match
        </ButtonText>
      </Button>
    </View>
  );
};

export default FilterSection;
