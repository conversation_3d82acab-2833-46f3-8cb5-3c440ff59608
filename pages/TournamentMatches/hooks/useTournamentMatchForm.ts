import { useState, useEffect, useMemo, useCallback } from 'react';
import { useRouter } from 'expo-router';
import { ScreenMode } from '@/components/k-components/EditableFormField';
import { toast } from '@/toast/toast';
import { PARTICIPANT_TYPES } from '@/types/participants';
import {
  type TournamentMatchFormData,
  getInitialTournamentMatchFormData,
} from '@/config/tournamentMatchFormConfig';
import { getRoundOptions } from '@/config/roundConfig';
import {
  addCustomRound,
  getCustomRounds,
  addCustomCourtField,
  getCustomCourtFields,
} from '@/services/tournamentService';
import { createAndMergeOptions, type Option } from '@/utils/option-utils';
import {
  createCustomOption,
  formatMatchData,
} from '@/utils/tournament-match-utils';
import { createMatch } from '@/services/matchService';
import {
  getParticipantType,
  getParticipantLabel,
  getParticipantPlaceholder,
  getParticipantNoDataMessage,
} from '../utils/participantUtils';
import { fetchTournamentById } from '@/services/tournamentService';
import { useParticipantData } from './useParticipantData';
import type { Tournament } from '@/types/tournament';
import type { ParticipantType } from '@/types/participants';

interface UseTournamentMatchFormProps {
  tournamentId: string;
  mode?: string;
}

interface UseTournamentMatchFormReturn {
  form: TournamentMatchFormData;
  errors: Record<string, string>;
  apiError: string | null;
  isSubmitting: boolean;
  loading: boolean;
  tournament: Tournament | null;
  screenMode: ScreenMode;
  title: string;
  submitButtonText: string;
  participantType: ParticipantType;
  participantLabel: string;
  teamAOptions: Option[];
  teamBOptions: Option[];
  hasMoreTeamA: boolean;
  hasMoreTeamB: boolean;
  placeholder: string;
  noDataMessage: string;
  roundOptions: Option[];
  courtFieldOptions: Option[];
  tournamentStartDate?: string | null;
  tournamentEndDate?: string | null;

  handleChange: (key: string, value: any) => void;
  handleSubmit: () => Promise<void>;
  validateForm: () => boolean;
  loadMoreTeamA: (searchQuery: string, page: number) => Promise<void>;
  loadMoreTeamB: (searchQuery: string, page: number) => Promise<void>;
  handleRoundCreate: (newRoundName: string) => Promise<Option>;
  handleCourtFieldCreate: (newCourtFieldName: string) => Promise<Option>;
}

export const useTournamentMatchForm = ({
  tournamentId,
  mode,
}: UseTournamentMatchFormProps): UseTournamentMatchFormReturn => {
  const router = useRouter();

  const screenMode = useMemo(
    () => (mode === 'edit' ? ScreenMode.EDIT : ScreenMode.CREATE),
    [mode]
  );

  const [form, setForm] = useState<TournamentMatchFormData>(() =>
    getInitialTournamentMatchFormData()
  );
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [apiError, setApiError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [tournament, setTournament] = useState<Tournament | null>(null);
  const [roundOptions, setRoundOptions] = useState(() => getRoundOptions());
  const [courtFieldOptions, setCourtFieldOptions] = useState<Option[]>([]);

  const participantType = useMemo(
    () => getParticipantType(tournament),
    [tournament]
  );
  const participantLabel = useMemo(
    () => getParticipantLabel(participantType),
    [participantType]
  );
  const placeholder = useMemo(
    () => getParticipantPlaceholder(participantType),
    [participantType]
  );
  const noDataMessage = useMemo(
    () => getParticipantNoDataMessage(participantType),
    [participantType]
  );

  // Use participant data hook
  const {
    teamAOptions,
    teamBOptions,
    hasMoreTeamA,
    hasMoreTeamB,
    loadMoreTeamA,
    loadMoreTeamB,
  } = useParticipantData({
    tournamentId,
    participantType,
    selectedTeamA: form.teamA || undefined,
    selectedTeamB: form.teamB || undefined,
  });

  // Load tournament data and custom options
  useEffect(() => {
    const loadTournament = async () => {
      try {
        setLoading(true);
        const { success, data, error } = await fetchTournamentById(
          tournamentId
        );

        if (!success || !data) {
          setApiError(error || 'Failed to load tournament');
          return;
        }

        setTournament(data);

        // Load custom rounds and court fields in parallel
        const [roundsResult, courtFieldsResult] = await Promise.all([
          getCustomRounds(tournamentId),
          getCustomCourtFields(tournamentId),
        ]);

        // Update round options (custom rounds at top + predefined rounds)
        if (roundsResult.success) {
          const predefinedRounds = getRoundOptions();
          setRoundOptions(
            createAndMergeOptions(roundsResult.rounds, predefinedRounds)
          );
        }

        // Update court field options (custom court fields at top)
        if (courtFieldsResult.success) {
          setCourtFieldOptions(
            createAndMergeOptions(courtFieldsResult.courtFields, [])
          );
        }
      } catch (error: any) {
        setApiError(error.message || 'Failed to load tournament');
      } finally {
        setLoading(false);
      }
    };

    if (tournamentId) {
      loadTournament();
    }
  }, [tournamentId]);

  const handleChange = useCallback(
    (key: string, value: any) => {
      setForm((prev) => ({ ...prev, [key]: value }));

      // Clear error when user makes changes
      if (errors[key]) {
        setErrors((prev) => ({ ...prev, [key]: '' }));
      }
    },
    [errors]
  );

  const validateForm = useCallback(() => {
    const newErrors: Record<string, string> = {};

    // Validate team selection difference
    if (form.teamA && form.teamB && form.teamA.value === form.teamB.value) {
      newErrors.teamB = `${participantLabel} B must be different from ${participantLabel} A`;
    }

    // Validate required fields based on config
    // scheduled_date is required
    if (!form.scheduled_date || form.scheduled_date.trim() === '') {
      newErrors.scheduled_date = 'Match Date & Time is required';
    }

    // stage is required
    if (!form.stage || !form.stage.value) {
      newErrors.stage = 'Match Round is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [form, participantLabel]);

  // Helper function to format participant names based on type
  const formatParticipantName = useCallback(
    (option: Option | null | undefined): string | null => {
      if (!option) return null;

      switch (participantType) {
        case PARTICIPANT_TYPES.PLAYER:
          return option.label; // Player name
        case PARTICIPANT_TYPES.TEAM:
          return option.label; // Team name
        case PARTICIPANT_TYPES.PAIR:
          // For pairs: "Player 1 / Player 2"
          if (option.players && option.players.length === 2) {
            return `${option.players[0].name} / ${option.players[1].name}`;
          }
          return option.label; // Fallback to team name
        default:
          return option.label;
      }
    },
    [participantType]
  );

  const handleSubmit = useCallback(async () => {
    setErrors({});
    setApiError(null);

    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);

      // Format the match data with required fields
      const matchData = formatMatchData(
        form,
        tournamentId,
        participantType,
        formatParticipantName
      );

      console.log('Tournament Match Data:', matchData);

      // Create the match
      const { success, error } = await createMatch(matchData);

      if (!success) {
        setApiError(error || 'Failed to create match');
        toast.error(error || 'Failed to create match. Please try again.');
        return;
      }

      toast.success('Match created successfully');
      router.back();
    } catch (error: any) {
      const errorMessage =
        error.message || 'Failed to create match. Please try again.';
      setApiError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  }, [
    validateForm,
    tournamentId,
    form,
    participantType,
    formatParticipantName,
    router,
  ]);

  // Handle creating new round options (save to database)
  const handleRoundCreate = useCallback(
    async (newRoundName: string): Promise<Option> => {
      return createCustomOption(
        newRoundName,
        (label) => addCustomRound(tournamentId, label),
        (newOption) => setRoundOptions((prev) => [newOption, ...prev]),
        `Round "${newRoundName}" created and selected`
      );
    },
    [tournamentId]
  );

  // Handle creating new court/field options (save to database)
  const handleCourtFieldCreate = useCallback(
    async (newCourtFieldName: string): Promise<Option> => {
      return createCustomOption(
        newCourtFieldName,
        (label) => addCustomCourtField(tournamentId, label),
        (newOption) => setCourtFieldOptions((prev) => [newOption, ...prev]),
        `Court/Field "${newCourtFieldName}" created and selected`
      );
    },
    [tournamentId]
  );

  const title = useMemo(
    () => (screenMode === ScreenMode.EDIT ? 'Edit Match' : 'Create Match'),
    [screenMode]
  );

  const submitButtonText = useMemo(
    () => (screenMode === ScreenMode.EDIT ? 'Update Match' : 'Create Match'),
    [screenMode]
  );

  return {
    form,
    errors,
    apiError,
    isSubmitting,
    loading,
    tournament,
    screenMode,
    title,
    submitButtonText,
    participantType,
    participantLabel,
    teamAOptions,
    teamBOptions,
    hasMoreTeamA,
    hasMoreTeamB,
    placeholder,
    noDataMessage,
    roundOptions,
    courtFieldOptions,
    tournamentStartDate: tournament?.start_date,
    tournamentEndDate: tournament?.end_date,
    handleChange,
    handleSubmit,
    validateForm,
    loadMoreTeamA,
    loadMoreTeamB,
    handleRoundCreate,
    handleCourtFieldCreate,
  };
};
