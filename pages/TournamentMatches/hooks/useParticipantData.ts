import { useState, useCallback, useEffect } from 'react';
import { fetchTeams, fetchPairs } from '@/services/teamsService';
import { fetchPlayers } from '@/services/playerService';
import { fetchPlayersForPairs } from '@/services/playerService';
import { PARTICIPANT_TYPES, type ParticipantType } from '@/types/participants';
import type { Option } from '@/components/k-components/AsyncSelectWithSearch';

interface UseParticipantDataProps {
  tournamentId: string;
  participantType: ParticipantType;
  selectedTeamA?: Option;
  selectedTeamB?: Option;
}

interface UseParticipantDataReturn {
  teamAOptions: Option[];
  teamBOptions: Option[];
  hasMoreTeamA: boolean;
  hasMoreTeamB: boolean;
  loadingTeamA: boolean;
  loadingTeamB: boolean;
  loadMoreTeamA: (searchQuery: string, page: number) => Promise<void>;
  loadMoreTeamB: (searchQuery: string, page: number) => Promise<void>;
  refreshData: () => void;
}

export const useParticipantData = ({
  tournamentId,
  participantType,
  selectedTeamA,
  selectedTeamB,
}: UseParticipantDataProps): UseParticipantDataReturn => {
  const [teamAOptions, setTeamAOptions] = useState<Option[]>([]);
  const [teamBOptions, setTeamBOptions] = useState<Option[]>([]);
  const [hasMoreTeamA, setHasMoreTeamA] = useState(false);
  const [hasMoreTeamB, setHasMoreTeamB] = useState(false);
  const [loadingTeamA, setLoadingTeamA] = useState(false);
  const [loadingTeamB, setLoadingTeamB] = useState(false);
  const [teamASearchQuery, setTeamASearchQuery] = useState('');
  const [teamBSearchQuery, setTeamBSearchQuery] = useState('');

  const transformToOptions = useCallback(
    async (data: any[], type: ParticipantType): Promise<Option[]> => {
      if (!data || data.length === 0) {
        return [];
      }

      const options: Option[] = [];
      const seenIds = new Set<string>();

      if (type === PARTICIPANT_TYPES.PAIR) {
        const pairIds = data.map((pair: any) => pair.id);
        const { playersByPairId, error: playersError } =
          await fetchPlayersForPairs({
            tournamentId,
            pairIds,
          });

        if (playersError && __DEV__) {
          console.warn('Some players failed to load for pairs:', playersError);
        }

        for (const item of data) {
          if (seenIds.has(item.id)) {
            continue;
          }
          seenIds.add(item.id);

          const players = playersByPairId[item.id] || [];

          const option: Option = {
            label: item.name,
            value: item.id,
            team: item,
            players: players,
          };
          options.push(option);
        }
      } else {
        for (const item of data) {
          if (seenIds.has(item.id)) {
            continue;
          }
          seenIds.add(item.id);

          let option: Option;

          switch (type) {
            case PARTICIPANT_TYPES.PLAYER:
              option = {
                label: item.name,
                value: item.id,
                player: item,
              };
              break;

            case PARTICIPANT_TYPES.TEAM:
            default:
              option = {
                label: item.name,
                value: item.id,
                team: item,
              };
              break;
          }

          options.push(option);
        }
      }

      return options;
    },
    [tournamentId]
  );

  const fetchParticipantData = useCallback(
    async (search: string = '', page: number = 1, limit: number = 10) => {
      try {
        let result;

        switch (participantType) {
          case PARTICIPANT_TYPES.PLAYER:
            result = await fetchPlayers({
              tournamentId,
              search,
              page,
              limit,
            });
            return {
              data: result.players || [],
              count: result.count || 0,
              error: result.error,
            };

          case PARTICIPANT_TYPES.PAIR:
            result = await fetchPairs({
              tournamentId,
              search,
              page,
              limit,
            });

            if (
              result.error &&
              result.error.includes('range not satisfiable')
            ) {
              if (page > 1) {
                result = await fetchPairs({
                  tournamentId,
                  search,
                  page: 1,
                  limit,
                });
              }
            }

            return {
              data: result.teams || [],
              count: result.count || 0,
              error: result.error,
            };

          case PARTICIPANT_TYPES.TEAM:
          default:
            result = await fetchTeams({
              tournamentId,
              search,
              page,
              limit,
            });
            return {
              data: result.teams || [],
              count: result.count || 0,
              error: result.error,
            };
        }
      } catch (error: any) {
        console.error('Error in fetchParticipantData:', error);
        return {
          data: [],
          count: 0,
          error: error.message || 'Failed to fetch data',
        };
      }
    },
    [tournamentId, participantType]
  );

  const filterExcludedOptions = useCallback(
    (options: Option[], excludedValue?: string): Option[] => {
      if (!excludedValue) return options;
      return options.filter((option) => option.value !== excludedValue);
    },
    []
  );

  const loadMoreTeamA = useCallback(
    async (searchQuery: string = '', page: number = 1) => {
      if (loadingTeamA) return;

      try {
        setLoadingTeamA(true);

        const isNewSearch = searchQuery !== teamASearchQuery;
        if (isNewSearch) {
          setTeamASearchQuery(searchQuery);
          setTeamAOptions([]);
        }

        const actualPage = isNewSearch ? 1 : page;

        const { data, count, error } = await fetchParticipantData(
          searchQuery,
          actualPage
        );

        if (error) {
          console.error('Error loading Team A options:', error);
          return;
        }

        const options = await transformToOptions(data, participantType);
        const filteredOptions = filterExcludedOptions(
          options,
          selectedTeamB?.value
        );

        if (actualPage === 1) {
          setTeamAOptions(filteredOptions);
        } else {
          setTeamAOptions((prev) => {
            const existingIds = new Set(prev.map((option) => option.value));
            const newOptions = filteredOptions.filter(
              (option) => !existingIds.has(option.value)
            );
            return [...prev, ...newOptions];
          });
        }

        setHasMoreTeamA(data.length === 10 && count > actualPage * 10);
      } catch (error) {
        console.error('Error loading Team A options:', error);
      } finally {
        setLoadingTeamA(false);
      }
    },
    [
      loadingTeamA,
      fetchParticipantData,
      transformToOptions,
      participantType,
      filterExcludedOptions,
      selectedTeamB?.value,
      teamASearchQuery,
    ]
  );

  const loadMoreTeamB = useCallback(
    async (searchQuery: string = '', page: number = 1) => {
      if (loadingTeamB) return;

      try {
        setLoadingTeamB(true);

        const isNewSearch = searchQuery !== teamBSearchQuery;
        if (isNewSearch) {
          setTeamBSearchQuery(searchQuery);
          setTeamBOptions([]);
        }

        const actualPage = isNewSearch ? 1 : page;

        const { data, count, error } = await fetchParticipantData(
          searchQuery,
          actualPage
        );

        if (error) {
          console.error('Error loading Team B options:', error);
          return;
        }

        const options = await transformToOptions(data, participantType);
        const filteredOptions = filterExcludedOptions(
          options,
          selectedTeamA?.value
        );

        if (actualPage === 1) {
          setTeamBOptions(filteredOptions);
        } else {
          setTeamBOptions((prev) => {
            const existingIds = new Set(prev.map((option) => option.value));
            const newOptions = filteredOptions.filter(
              (option) => !existingIds.has(option.value)
            );
            return [...prev, ...newOptions];
          });
        }

        setHasMoreTeamB(data.length === 10 && count > actualPage * 10);
      } catch (error) {
        console.error('Error loading Team B options:', error);
      } finally {
        setLoadingTeamB(false);
      }
    },
    [
      loadingTeamB,
      fetchParticipantData,
      transformToOptions,
      participantType,
      filterExcludedOptions,
      selectedTeamA?.value,
      teamBSearchQuery,
    ]
  );

  const refreshData = useCallback(() => {
    setTeamAOptions([]);
    setTeamBOptions([]);
    loadMoreTeamA('', 1);
    loadMoreTeamB('', 1);
  }, [loadMoreTeamA, loadMoreTeamB]);

  useEffect(() => {
    if (tournamentId && participantType) {
      setTeamAOptions([]);
      setTeamBOptions([]);
      setTeamASearchQuery('');
      setTeamBSearchQuery('');

      loadMoreTeamA('', 1);
      loadMoreTeamB('', 1);
    }
  }, [tournamentId, participantType]);

  useEffect(() => {
    if (selectedTeamA || selectedTeamB) {
      setTeamAOptions((prev) =>
        filterExcludedOptions(prev, selectedTeamB?.value)
      );
      setTeamBOptions((prev) =>
        filterExcludedOptions(prev, selectedTeamA?.value)
      );
    }
  }, [selectedTeamA?.value, selectedTeamB?.value, filterExcludedOptions]);

  return {
    teamAOptions,
    teamBOptions,
    hasMoreTeamA,
    hasMoreTeamB,
    loadingTeamA,
    loadingTeamB,
    loadMoreTeamA,
    loadMoreTeamB,
    refreshData,
  };
};
