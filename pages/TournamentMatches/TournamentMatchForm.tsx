import React from 'react';
import { ScrollView, View } from 'react-native';
import { useRouter } from 'expo-router';
import { NavLayout } from '@/components/NavLayout';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import { StickyBottomButtons } from '@/components/k-components/StickyBottomButtons';
import { useTournamentMatchForm } from './hooks/useTournamentMatchForm';
import TournamentMatchFormFields from './components/TournamentMatchFormFields';

interface TournamentMatchFormProps {
  tournamentId: string;
  mode?: string;
}

const TournamentMatchForm: React.FC<TournamentMatchFormProps> = ({
  tournamentId,
  mode,
}) => {
  const router = useRouter();

  const {
    form,
    errors,
    apiError,
    isSubmitting,
    loading,
    title,
    submitButtonText,
    participantType,
    participantLabel,
    teamAOptions,
    teamBOptions,
    hasMoreTeamA,
    hasMoreTeamB,
    placeholder,
    noDataMessage,
    roundOptions,
    courtFieldOptions,
    tournamentStartDate,
    tournamentEndDate,
    handleChange,
    handleSubmit,
    loadMoreTeamA,
    loadMoreTeamB,
    handleRoundCreate,
    handleCourtFieldCreate,
  } = useTournamentMatchForm({ tournamentId, mode });

  if (loading) {
    return <FullscreenLoader />;
  }

  return (
    <NavLayout title={title} noScroll>
      <View className="flex-1">
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 120 }}
        >
          <View className="px-4 py-4">
            <TournamentMatchFormFields
              form={form}
              errors={errors}
              participantType={participantType}
              participantLabel={participantLabel}
              teamAOptions={teamAOptions}
              teamBOptions={teamBOptions}
              hasMoreTeamA={hasMoreTeamA}
              hasMoreTeamB={hasMoreTeamB}
              placeholder={placeholder}
              noDataMessage={noDataMessage}
              roundOptions={roundOptions}
              courtFieldOptions={courtFieldOptions}
              tournamentStartDate={tournamentStartDate}
              tournamentEndDate={tournamentEndDate}
              onChange={handleChange}
              onLoadMoreTeamA={loadMoreTeamA}
              onLoadMoreTeamB={loadMoreTeamB}
              onRoundCreate={handleRoundCreate}
              onCourtFieldCreate={handleCourtFieldCreate}
              apiError={apiError}
            />
          </View>
        </ScrollView>

        <StickyBottomButtons
          primaryButton={{
            title: submitButtonText,
            onPress: handleSubmit,
            loading: isSubmitting,
            disabled: false,
          }}
          secondaryButton={{
            title: 'Cancel',
            onPress: () => router.back(),
            variant: 'cancel',
          }}
        />
      </View>
    </NavLayout>
  );
};

export default TournamentMatchForm;
