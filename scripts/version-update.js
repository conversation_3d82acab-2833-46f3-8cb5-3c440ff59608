const { execSync } = require('child_process');
const readline = require('readline');
const fs = require('fs');
const path = require('path');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

console.log('🚀 Kali Version Manager\n');

const appJsonPath = path.join(__dirname, '../app.json');
const packageJsonPath = path.join(__dirname, '../package.json');
const testContentPath = path.join(__dirname, '../app/debug-updates.tsx');

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

function getCurrentVersion() {
  try {
    const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    return appJson.expo.version;
  } catch (error) {
    console.error('❌ Failed to read current version:', error.message);
    return null;
  }
}

function parseVersion(version) {
  const parts = version.split('.').map(Number);
  return {
    major: parts[0] || 0,
    minor: parts[1] || 0,
    patch: parts[2] || 0,
  };
}

function incrementVersion(currentVersion, type) {
  const version = parseVersion(currentVersion);

  switch (type) {
    case 'major':
      version.major += 1;
      version.minor = 0;
      version.patch = 0;
      break;
    case 'minor':
      version.minor += 1;
      version.patch = 0;
      break;
    case 'patch':
      version.patch += 1;
      break;
    default:
      throw new Error('Invalid version type');
  }

  return `${version.major}.${version.minor}.${version.patch}`;
}

function updateAppJson(newVersion) {
  try {
    const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    const oldVersion = appJson.expo.version;

    // Update version
    appJson.expo.version = newVersion;

    // Auto-increment Android versionCode
    if (appJson.expo.android && appJson.expo.android.versionCode) {
      appJson.expo.android.versionCode += 1;
      console.log(
        `📱 Android versionCode incremented to ${appJson.expo.android.versionCode}`
      );
    }

    fs.writeFileSync(appJsonPath, JSON.stringify(appJson, null, 2));
    console.log(`✅ Updated app.json: ${oldVersion} → ${newVersion}`);
    return true;
  } catch (error) {
    console.error('❌ Failed to update app.json:', error.message);
    return false;
  }
}

function updatePackageJson(newVersion) {
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    packageJson.version = newVersion;
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log(`✅ Updated package.json to version ${newVersion}`);
    return true;
  } catch (error) {
    console.error('❌ Failed to update package.json:', error.message);
    return false;
  }
}

function updateTestContent(newVersion) {
  try {
    let content = fs.readFileSync(testContentPath, 'utf8');
    const timestamp = new Date().toLocaleString();

    // Replace the test content
    content = content.replace(
      /Version: [\d\.]+ - .+/,
      `Version: ${newVersion} - Updated ${timestamp}`
    );

    fs.writeFileSync(testContentPath, content);
    console.log(`✅ Updated test content to version ${newVersion}`);
    return true;
  } catch (error) {
    console.error('❌ Failed to update test content:', error.message);
    return false;
  }
}

function publishUpdate(channel, message) {
  try {
    console.log(`\n🚀 Publishing update to ${channel} channel...`);
    const command = `eas update --branch ${channel} --message "${message}"`;
    console.log(`Running: ${command}\n`);

    execSync(command, { stdio: 'inherit' });
    console.log(`\n✅ Update published successfully to ${channel}!`);
    return true;
  } catch (error) {
    console.error(`\n❌ Failed to publish update:`, error.message);
    return false;
  }
}

async function main() {
  try {
    const currentVersion = getCurrentVersion();
    if (!currentVersion) {
      process.exit(1);
    }

    console.log(`📋 Current version: ${currentVersion}\n`);

    // Ask for version type
    console.log('What type of update is this?');
    console.log('1. Patch (bug fixes, small changes)');
    console.log('2. Minor (new features, significant changes)');
    console.log('3. Major (breaking changes, major releases)');

    const versionChoice = await askQuestion('\nSelect version type (1-3): ');

    let versionType;
    switch (versionChoice) {
      case '1':
        versionType = 'patch';
        break;
      case '2':
        versionType = 'minor';
        break;
      case '3':
        versionType = 'major';
        break;
      default:
        console.log('❌ Invalid choice');
        process.exit(1);
    }

    const newVersion = incrementVersion(currentVersion, versionType);
    console.log(`\n📈 New version will be: ${newVersion}`);

    // Ask for update message
    const message = await askQuestion('\nEnter update message: ');
    if (!message.trim()) {
      console.log('❌ Update message is required');
      process.exit(1);
    }

    // Ask for channel
    console.log('\nSelect update channel:');
    console.log('1. Development');
    console.log('2. Preview');
    console.log('3. Production');

    const channelChoice = await askQuestion('\nSelect channel (1-3): ');

    let channel;
    switch (channelChoice) {
      case '1':
        channel = 'development';
        break;
      case '2':
        channel = 'preview';
        break;
      case '3':
        channel = 'production';
        break;
      default:
        console.log('❌ Invalid channel choice');
        process.exit(1);
    }

    // Confirm
    const confirm = await askQuestion(`\n📋 Summary:
Current version: ${currentVersion}
New version: ${newVersion} (${versionType})
Channel: ${channel}
Message: ${message}

Proceed? (y/N): `);

    if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
      console.log('❌ Update cancelled');
      process.exit(0);
    }

    // Update version files
    console.log('\n🔄 Updating version files...');

    if (!updateAppJson(newVersion)) process.exit(1);
    if (!updatePackageJson(newVersion)) process.exit(1);
    if (!updateTestContent(newVersion)) process.exit(1);

    // Publish update
    const fullMessage = `${message} (v${newVersion})`;
    const success = publishUpdate(channel, fullMessage);

    if (success) {
      console.log(`\n🎉 Successfully updated to version ${newVersion}!`);

      if (versionType === 'minor' || versionType === 'major') {
        console.log(
          '\n⚠️  Note: Minor/Major version changes may require a new build:'
        );
        console.log(
          `eas build --platform all --profile ${
            channel === 'production' ? 'production' : 'preview'
          }`
        );
      }

      console.log('\n📱 Testing Instructions:');
      console.log('1. Open your built app (not Expo Go)');
      console.log('2. Navigate to /debug-updates screen');
      console.log('3. Check for updates or wait for automatic update');
      console.log('4. Verify the new version is displayed');
    }
  } catch (error) {
    console.error('\n❌ Error:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

main();
