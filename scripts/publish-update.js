#!/usr/bin/env node

const { execSync } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

const channels = ['development', 'preview', 'production'];

console.log('⚠️  Note: This script publishes updates without version changes.');
console.log('💡 For version updates, use: yarn version-update\n');

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function main() {
  try {
    console.log('🚀 Expo Updates Publisher\n');

    // Select channel
    console.log('Available channels:');
    channels.forEach((channel, index) => {
      console.log(`${index + 1}. ${channel}`);
    });

    const channelChoice = await askQuestion('\nSelect channel (1-3): ');
    const channelIndex = parseInt(channelChoice) - 1;

    if (channelIndex < 0 || channelIndex >= channels.length) {
      console.log('❌ Invalid channel selection');
      process.exit(1);
    }

    const selectedChannel = channels[channelIndex];
    console.log(`✅ Selected channel: ${selectedChannel}`);

    // Get update message
    const message = await askQuestion('\nEnter update message: ');

    if (!message.trim()) {
      console.log('❌ Update message is required');
      process.exit(1);
    }

    // Confirm
    const confirm = await askQuestion(`\n📋 Summary:
Channel: ${selectedChannel}
Message: ${message}

Proceed? (y/N): `);

    if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
      console.log('❌ Update cancelled');
      process.exit(0);
    }

    // Publish update
    console.log('\n🔄 Publishing update...');

    const command = `eas update --branch ${selectedChannel} --message "${message}"`;
    console.log(`Running: ${command}\n`);

    execSync(command, { stdio: 'inherit' });

    console.log('\n✅ Update published successfully!');
    console.log(
      `\n📱 Users on the "${selectedChannel}" channel will receive this update.`
    );
  } catch (error) {
    console.error('\n❌ Error publishing update:', error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

main();
