import { useState, useEffect, useCallback } from 'react';
import * as Updates from 'expo-updates';
import {
  checkForUpdates,
  downloadAndInstallUpdate,
  getCurrentUpdateInfo,
  UpdateInfo,
} from '../updates';

export interface UseUpdatesReturn {
  // State
  isChecking: boolean;
  isDownloading: boolean;
  updateInfo: UpdateInfo | null;
  error: string | null;

  // Actions
  checkForUpdates: () => Promise<void>;
  downloadAndInstall: () => Promise<void>;

  // Info
  currentUpdateInfo: ReturnType<typeof getCurrentUpdateInfo>;
}

export function useUpdates(): UseUpdatesReturn {
  const [isChecking, setIsChecking] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [updateInfo, setUpdateInfo] = useState<UpdateInfo | null>(null);
  const [error, setError] = useState<string | null>(null);

  const currentUpdateInfo = getCurrentUpdateInfo();

  const handleCheckForUpdates = useCallback(async () => {
    if (__DEV__) {
      console.log('Updates not available in development mode');
      return;
    }

    setIsChecking(true);
    setError(null);

    try {
      const info = await checkForUpdates();
      setUpdateInfo(info);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to check for updates'
      );
    } finally {
      setIsChecking(false);
    }
  }, []);

  const handleDownloadAndInstall = useCallback(async () => {
    if (__DEV__) {
      console.log('Updates not available in development mode');
      return;
    }

    setIsDownloading(true);
    setError(null);

    try {
      const success = await downloadAndInstallUpdate();
      if (!success) {
        setError('No new update available or failed to install');
      }
    } catch (err) {
      setError(
        err instanceof Error
          ? err.message
          : 'Failed to download and install update'
      );
    } finally {
      setIsDownloading(false);
    }
  }, []);

  // Auto-check for updates on mount (optional)
  useEffect(() => {
    if (!__DEV__) {
      handleCheckForUpdates();
    }
  }, [handleCheckForUpdates]);

  return {
    isChecking,
    isDownloading,
    updateInfo,
    error,
    checkForUpdates: handleCheckForUpdates,
    downloadAndInstall: handleDownloadAndInstall,
    currentUpdateInfo,
  };
}

/**
 * Hook for automatic background updates
 */
export function useBackgroundUpdates() {
  const [hasUpdate, setHasUpdate] = useState(false);

  useEffect(() => {
    if (__DEV__) return;

    const checkForBackgroundUpdate = async () => {
      try {
        const update = await Updates.checkForUpdateAsync();
        if (update.isAvailable) {
          // Download in background
          await Updates.fetchUpdateAsync();
          setHasUpdate(true);
        }
      } catch (error) {
        console.error('Background update check failed:', error);
      }
    };

    // Check immediately
    checkForBackgroundUpdate();

    // Set up periodic checks (every 30 minutes)
    const interval = setInterval(checkForBackgroundUpdate, 30 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  const applyUpdate = useCallback(async () => {
    if (hasUpdate) {
      await Updates.reloadAsync();
    }
  }, [hasUpdate]);

  return {
    hasUpdate,
    applyUpdate,
  };
}

/**
 * Hook for completely silent auto-updates (downloads and installs immediately)
 * Use with caution - this will restart the app without warning
 */
export function useAutoSilentUpdates(
  options: {
    checkInterval?: number; // in milliseconds, default 30 minutes
    onUpdateStart?: () => void;
    onUpdateComplete?: () => void;
    onUpdateError?: (error: string) => void;
  } = {}
) {
  const {
    checkInterval = 30 * 60 * 1000, // 30 minutes
    onUpdateStart,
    onUpdateComplete,
    onUpdateError,
  } = options;

  useEffect(() => {
    if (__DEV__) return;

    const performAutoUpdate = async () => {
      try {
        onUpdateStart?.();

        const update = await Updates.checkForUpdateAsync();

        if (update.isAvailable) {
          console.log('Auto-update: Downloading and installing update...');
          const fetchResult = await Updates.fetchUpdateAsync();

          if (fetchResult.isNew) {
            onUpdateComplete?.();
            // Small delay to allow any cleanup
            setTimeout(() => {
              Updates.reloadAsync();
            }, 1000);
          }
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error';
        console.error('Auto-update failed:', errorMessage);
        onUpdateError?.(errorMessage);
      }
    };

    // Check immediately on mount
    performAutoUpdate();

    // Set up periodic checks
    const interval = setInterval(performAutoUpdate, checkInterval);

    return () => clearInterval(interval);
  }, [checkInterval, onUpdateStart, onUpdateComplete, onUpdateError]);
}

/**
 * Hook for silent downloads with manual install trigger
 * Downloads updates in background, but waits for manual trigger to install
 */
export function useSilentDownloadUpdates(
  options: {
    checkInterval?: number;
    onUpdateDownloaded?: () => void;
    onDownloadError?: (error: string) => void;
  } = {}
) {
  const [updateReady, setUpdateReady] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  const {
    checkInterval = 30 * 60 * 1000, // 30 minutes
    onUpdateDownloaded,
    onDownloadError,
  } = options;

  useEffect(() => {
    if (__DEV__) return;

    const downloadUpdate = async () => {
      if (isDownloading || updateReady) return;

      try {
        setIsDownloading(true);

        const update = await Updates.checkForUpdateAsync();

        if (update.isAvailable) {
          console.log('Silent download: Update available, downloading...');
          const fetchResult = await Updates.fetchUpdateAsync();

          if (fetchResult.isNew) {
            setUpdateReady(true);
            onUpdateDownloaded?.();
            console.log('Silent download: Update ready to install');
          }
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error';
        console.error('Silent download failed:', errorMessage);
        onDownloadError?.(errorMessage);
      } finally {
        setIsDownloading(false);
      }
    };

    // Check immediately
    downloadUpdate();

    // Set up periodic checks
    const interval = setInterval(downloadUpdate, checkInterval);

    return () => clearInterval(interval);
  }, [
    checkInterval,
    isDownloading,
    updateReady,
    onUpdateDownloaded,
    onDownloadError,
  ]);

  const installUpdate = useCallback(async () => {
    if (updateReady) {
      try {
        await Updates.reloadAsync();
      } catch (error) {
        console.error('Failed to install update:', error);
      }
    }
  }, [updateReady]);

  return {
    updateReady,
    isDownloading,
    installUpdate,
  };
}
