import * as Updates from 'expo-updates';
import { Alert } from 'react-native';

export interface UpdateInfo {
  isUpdateAvailable: boolean;
  isUpdatePending: boolean;
  manifest?: Updates.Manifest;
}

/**
 * Check for available updates
 */
export async function checkForUpdates(): Promise<UpdateInfo> {
  try {
    if (__DEV__) {
      console.log('Updates are not available in development mode');
      return {
        isUpdateAvailable: false,
        isUpdatePending: false,
      };
    }

    const update = await Updates.checkForUpdateAsync();

    return {
      isUpdateAvailable: update.isAvailable,
      isUpdatePending: false,
      manifest: update.manifest,
    };
  } catch (error) {
    console.error('Error checking for updates:', error);
    return {
      isUpdateAvailable: false,
      isUpdatePending: false,
    };
  }
}

/**
 * Download and install available update
 */
export async function downloadAndInstallUpdate(): Promise<boolean> {
  try {
    if (__DEV__) {
      console.log('Updates are not available in development mode');
      return false;
    }

    const update = await Updates.fetchUpdateAsync();

    if (update.isNew) {
      await Updates.reloadAsync();
      return true;
    }

    return false;
  } catch (error) {
    console.error('Error downloading update:', error);
    return false;
  }
}

/**
 * Check for updates and prompt user to install if available
 */
export async function checkAndPromptForUpdate(): Promise<void> {
  try {
    const updateInfo = await checkForUpdates();

    if (updateInfo.isUpdateAvailable) {
      Alert.alert(
        'Update Available',
        'A new version of the app is available. Would you like to update now?',
        [
          {
            text: 'Later',
            style: 'cancel',
          },
          {
            text: 'Update',
            onPress: async () => {
              const success = await downloadAndInstallUpdate();
              if (!success) {
                Alert.alert(
                  'Update Failed',
                  'Failed to download the update. Please try again later.'
                );
              }
            },
          },
        ]
      );
    }
  } catch (error) {
    console.error('Error in update check:', error);
  }
}

/**
 * Get current update information
 */
export function getCurrentUpdateInfo() {
  return {
    updateId: Updates.updateId,
    channel: Updates.channel,
    runtimeVersion: Updates.runtimeVersion,
    isEmbeddedLaunch: Updates.isEmbeddedLaunch,
    isEmergencyLaunch: Updates.isEmergencyLaunch,
  };
}

/**
 * Silent update check and download (for background updates)
 */
export async function silentUpdateCheck(): Promise<boolean> {
  try {
    if (__DEV__) {
      return false;
    }

    const update = await Updates.checkForUpdateAsync();

    if (update.isAvailable) {
      // Download the update in the background
      await Updates.fetchUpdateAsync();
      return true;
    }

    return false;
  } catch (error) {
    console.error('Error in silent update check:', error);
    return false;
  }
}

/**
 * Automatically check, download and install updates silently
 * This will restart the app if an update is available
 */
export async function autoUpdateSilently(): Promise<boolean> {
  try {
    if (__DEV__) {
      console.log('Auto-updates not available in development mode');
      return false;
    }

    const update = await Updates.checkForUpdateAsync();

    if (update.isAvailable) {
      console.log('Update available, downloading...');
      const fetchResult = await Updates.fetchUpdateAsync();

      if (fetchResult.isNew) {
        console.log('New update downloaded, restarting app...');
        await Updates.reloadAsync();
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('Error in auto-update:', error);
    return false;
  }
}

/**
 * Check and download updates silently, but don't install immediately
 * Returns true if an update was downloaded and is ready to install
 */
export async function downloadUpdateSilently(): Promise<boolean> {
  try {
    if (__DEV__) {
      return false;
    }

    const update = await Updates.checkForUpdateAsync();

    if (update.isAvailable) {
      console.log('Update available, downloading silently...');
      const fetchResult = await Updates.fetchUpdateAsync();
      return fetchResult.isNew;
    }

    return false;
  } catch (error) {
    console.error('Error downloading update silently:', error);
    return false;
  }
}

/**
 * Install a previously downloaded update
 */
export async function installDownloadedUpdate(): Promise<void> {
  try {
    if (__DEV__) {
      console.log('Updates not available in development mode');
      return;
    }

    await Updates.reloadAsync();
  } catch (error) {
    console.error('Error installing downloaded update:', error);
  }
}
