import React from 'react';
import { Tabs } from 'expo-router';
import { getTabOptions } from '@/utils';
import { useConnectionMonitor } from '@/hooks/useConnectionMonitor';

export default function TabLayout() {
  useConnectionMonitor();
  return (
    //@ts-ignore
    <Tabs screenOptions={({ route }) => getTabOptions(route.name)}>
      <Tabs.Screen name="explore" />
      <Tabs.Screen name="manage" />
      <Tabs.Screen name="account" />
    </Tabs>
  );
}
