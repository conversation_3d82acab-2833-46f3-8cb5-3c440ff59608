import { View } from 'react-native';
import { useForceRerenderOnFocus } from '@/hooks/useForceRerenderOnFocus';
import { StatusBar } from 'expo-status-bar';
import { AccountHeader } from '@/components/k-components/AccountHeader';
import { AccountSettingsList } from '@/components/k-components/AccountSettingsList';
import { AccountLogoutSection } from '@/components/k-components/AccountLogoutSection';
import AppVersionInfo from '@/components/AppVersionInfo';

export default function Account() {
  useForceRerenderOnFocus();

  return (
    <View style={{ flex: 1 }} className="bg-white relative">
      <StatusBar
        style="dark" // dark-content
        translucent
        backgroundColor="transparent"
      />
      <AccountHeader />

      <View className="px-8 py-4">
        <AccountSettingsList />
        <AccountLogoutSection />
      </View>
      <AppVersionInfo />
    </View>
  );
}
