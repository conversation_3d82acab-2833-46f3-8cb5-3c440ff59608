import React, { useState, useCallback } from 'react';
import { View, Text, ActivityIndicator, FlatList } from 'react-native';
import { useLocalSearchParams } from 'expo-router';
import { useFocusEffect } from '@react-navigation/native';
import { VIEW_ALL_CONFIG } from '@/config/viewAllConfig';
import SearchBar from '@/components/k-components/SearchBar';
import { NavLayout } from '@/components/NavLayout';
import NoDataFound from '@/components/k-components/NoDataFound';

export default function ViewAllScreen() {
  const params = useLocalSearchParams();
  const { eventType = '' } = params;

  const config = VIEW_ALL_CONFIG[eventType as string];

  const [items, setItems] = useState<any[]>([]);
  const [query, setQuery] = useState('');
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const [isFetchingMore, setIsFetchingMore] = useState(false);
  const [totalCount, setTotalCount] = useState<number>(0);

  const { eventType: _, ...extraParams } = params;

  useFocusEffect(
    useCallback(() => {
      setPage(1);
      loadData(1);
    }, [query])
  );

  const loadData = async (pageNumber = 1) => {
    if (!config) return;

    setLoading(pageNumber === 1);
    setIsFetchingMore(pageNumber > 1);

    const res = await config.fetchData(query, 10, {
      ...extraParams,
      page: pageNumber,
    });

    if (pageNumber === 1) {
      setItems(res.data);
    } else {
      setItems((prev) => [...prev, ...res.data]);
    }

    setTotalCount(res.totalCount ?? 0);
    setPage(pageNumber);
    setIsFetchingMore(false);
    setLoading(false);
  };

  const loadMore = async () => {
    if (isFetchingMore) return;
    const hasMore = items.length < totalCount;
    if (!hasMore) return;
    await loadData(page + 1);
  };

  if (!config) {
    return (
      <View className="p-6">
        <Text className="text-lg text-red-500">Invalid event type</Text>
      </View>
    );
  }

  return (
    <NavLayout
      title={config.title || ''}
      noScroll
      isFullscreen
      className="px-7"
    >
      <View className="flex-1 bg-white py-4 px-6">
        <SearchBar
          value={query}
          onDebouncedChange={setQuery}
          placeholder={`Search...`}
        />

        {loading ? (
          <ActivityIndicator size="large" color="#1DB960" />
        ) : query && items.length === 0 ? (
          <NoDataFound
            title="No Data Found"
            subtitle={`No results found for "${query}". Try adjusting your search terms.`}
          />
        ) : (
          <FlatList
            data={items}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => config.renderCard(item, extraParams)}
            onEndReached={loadMore}
            onEndReachedThreshold={0.4}
            contentContainerClassName="mb-3"
            showsVerticalScrollIndicator={false}
            ListFooterComponent={
              isFetchingMore ? (
                <ActivityIndicator
                  size="small"
                  color="#1DB960"
                  className="mt-2"
                />
              ) : null
            }
          />
        )}
      </View>
    </NavLayout>
  );
}
