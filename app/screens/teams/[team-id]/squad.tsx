import { useRouter, useLocalSearchParams } from 'expo-router';
import { NavLayout } from '@/components/NavLayout';
import SquadPage from '@/pages/Teams/SquadPage';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import { useTournamentById } from '@/hooks/useTournamentById';
import { useState, useCallback } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import { fetchTeamById } from '@/services/teamsService';
import { toast } from '@/toast/toast';
import { type Team } from '@/types/teams';

export default function TeamSquadScreen() {
  const router = useRouter();
  const {
    'team-id': teamIdParam,
    'tournament-id': tournamentIdParam,
    mode = 'create',
  } = useLocalSearchParams();

  const teamId = teamIdParam as string | undefined;
  const tournamentId = tournamentIdParam as string;

  const [team, setTeam] = useState<Team | null>(null);
  const [loading, setLoading] = useState(true);

  const { tournament, loading: tournamentLoading } =
    useTournamentById(tournamentId);

  const loadTeam = useCallback(async () => {
    if (!teamId) return;

    setLoading(true);
    const { success, team: fetchedTeam, error } = await fetchTeamById(teamId);

    if (!success || !fetchedTeam) {
      toast.error(error || 'Failed to load team details');
      router.back();
    } else {
      setTeam(fetchedTeam);
    }

    setLoading(false);
  }, [teamId, router]);

  useFocusEffect(
    useCallback(() => {
      loadTeam();
    }, [loadTeam])
  );

  const title = mode === 'edit' ? 'Edit Squad' : 'Create Squad';

  return (
    <NavLayout title={title} isFullscreen noScroll>
      {loading || tournamentLoading ? (
        <FullscreenLoader />
      ) : team && tournament ? (
        <SquadPage
          team={team}
          tournament={tournament}
          mode={mode as 'create' | 'edit'}
        />
      ) : null}
    </NavLayout>
  );
}
