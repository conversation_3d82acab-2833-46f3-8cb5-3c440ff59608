import { useRouter, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { useCallback, useState } from 'react';
import { NavLayout } from '@/components/NavLayout';
import TeamForm from '@/pages/Teams/TeamForm';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import { type TeamFormData, type Team } from '@/types/teams';
import { updateTeam, fetchTeamById } from '@/services/teamsService';
import { toast } from '@/toast/toast';

export default function EditTeamModal() {
  const router = useRouter();
  const { 'team-id': teamId, 'tournament-id': tournamentId } =
    useLocalSearchParams();
  const [team, setTeam] = useState<Team | null>(null);
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const loadTeam = useCallback(async () => {
    if (!teamId) return;

    setLoading(true);
    const {
      success,
      team: fetchedTeam,
      error,
    } = await fetchTeamById(teamId as string);

    if (!success) {
      toast.error(error || 'Failed to load team details');
      router.back();
      return;
    }

    setTeam(fetchedTeam);
    setLoading(false);
  }, [teamId, router]);

  useFocusEffect(
    useCallback(() => {
      loadTeam();
    }, [loadTeam])
  );

  const handleSubmit = async (formData: TeamFormData) => {
    if (!teamId) return;

    setIsSubmitting(true);
    const { success, error } = await updateTeam({
      teamId: teamId as string,
      team: formData,
    });

    setIsSubmitting(false);

    if (!success) {
      toast.error(error || 'Failed to update team. Please try again.');
      return;
    }

    toast.success('Team updated successfully');
    router.back();
  };

  return (
    <NavLayout title="Edit Team">
      {loading ? (
        <FullscreenLoader />
      ) : team ? (
        <TeamForm
          initialData={team}
          onSubmit={handleSubmit}
          submitButtonText="Update Team"
          isLoading={isSubmitting}
          tournamentId={tournamentId as string}
          isEditMode={true}
        />
      ) : null}
    </NavLayout>
  );
}
