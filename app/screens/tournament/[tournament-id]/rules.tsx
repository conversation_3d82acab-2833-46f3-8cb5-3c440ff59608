import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { NavLayout } from '@/components/NavLayout';
import { VStack } from '@/components/ui/vstack';
import { Button, ButtonText } from '@/components/ui/button';
import { sportRulesConfig, type SportRule } from '@/config/sportRulesConfig';
import {
  updateTournamentFields,
  fetchTournamentById,
} from '@/services/tournamentService';
import FormField from '@/components/k-components/FormField';
import { toast } from '@/toast/toast';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import WarningDialog from '@/components/k-components/WarningDialog';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import { isEmpty } from 'lodash';

export enum ScreenMode {
  CREATE = 'create',
  EDIT = 'edit',
}

function useEditableField(rule: SportRule, screenMode: ScreenMode) {
  const [canEdit, setCanEdit] = useState(rule.editable?.type !== 'warning');
  const shouldBlockEdit =
    screenMode === ScreenMode.EDIT &&
    rule.editable?.type === 'warning' &&
    !canEdit;

  return {
    canEdit:
      screenMode === ScreenMode.CREATE ||
      rule.editable?.type === 'editable' ||
      canEdit,
    shouldBlockEdit,
    setCanEdit,
    editableMeta: rule.editable,
  };
}

const RuleFieldWrapper = React.memo(
  ({
    field,
    screenMode,
    formValue,
    error,
    onChange,
  }: RuleFieldWrapperProps) => {
    const [showWarning, setShowWarning] = useState(false);
    const { canEdit, shouldBlockEdit, setCanEdit, editableMeta } =
      useEditableField(field, screenMode);

    const handleConfirm = useCallback(() => {
      setCanEdit(true);
      setShowWarning(false);
      if (formValue == null || formValue === undefined) {
        onChange(field.key, '');
      }
    }, [field.key, formValue, onChange]);

    return (
      <View key={field.key}>
        <FormField
          key={`${field.key}-${screenMode}`}
          keyName={field.key}
          type={field.type}
          field={field}
          value={formValue ?? ''}
          error={error}
          onChange={onChange}
          disabled={!canEdit}
        />
        {shouldBlockEdit && editableMeta?.type === 'warning' && (
          <Button variant="link" size="xs" onPress={() => setShowWarning(true)}>
            <Text className="font-urbanistSemiBold text-xs border-b border-typography-600 text-typography-600 self-start">
              Edit This Field
            </Text>
          </Button>
        )}
        {editableMeta?.type === 'disabled' && (
          <Text className="text-xs text-yellow-600 font-urbanist mb-1">
            {editableMeta.message}
          </Text>
        )}
        <WarningDialog
          open={showWarning}
          message={
            editableMeta?.type === 'warning' ||
            editableMeta?.type === 'disabled'
              ? editableMeta.message
              : 'Changing this will reset associated data. Are you sure?'
          }
          onClose={() => setShowWarning(false)}
          onConfirm={handleConfirm}
        />
      </View>
    );
  }
);

interface RuleFieldWrapperProps {
  field: SportRule;
  screenMode: ScreenMode;
  formValue: any;
  error: string;
  onChange: (key: string, value: any) => void;
}

export default function TournamentRulesScreen() {
  const {
    'tournament-id': tournamentId,
    sport_type,
    mode,
  } = useLocalSearchParams();

  const screenMode = (mode as ScreenMode) ?? ScreenMode.CREATE;
  const config = useMemo(
    () => sportRulesConfig[sport_type as keyof typeof sportRulesConfig],
    [sport_type]
  );
  const router = useRouter();

  const [form, setForm] = useState<Record<string, any>>(() => {
    return config.reduce((acc, field) => {
      acc[field.key] = field.default ?? null;
      return acc;
    }, {} as Record<string, any>);
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadTournamentData = async () => {
      if (screenMode === ScreenMode.EDIT && tournamentId) {
        const { success, data, error } = await fetchTournamentById(
          tournamentId as string
        );
        if (!success || !data) {
          toast.error(error || 'Failed to load tournament details');
          return;
        }
        setForm(data.tournament_rules || {});
      }
      setLoading(false);
    };

    loadTournamentData();
  }, [screenMode, tournamentId]);

  const handleChange = useCallback((key: string, value: any) => {
    setForm((prev) => ({
      ...prev,
      [key]: value,
    }));
    setErrors((prev) => ({ ...prev, [key]: '' }));
  }, []);

  const handleSubmit = useCallback(async () => {
    const newErrors = Object.fromEntries(
      config
        .filter((f) => f.required && isEmpty(form[f.key]))
        .map((f) => [f.key, `${f.label} is required`])
    );

    if (Object.keys(newErrors).length) {
      setErrors(newErrors);
      return;
    }

    const { success, error } = await updateTournamentFields(
      tournamentId as string,
      { tournament_rules: form }
    );

    if (!success) return toast.error(error || 'Something went wrong');
    router.canGoBack() && router.back();
  }, [config, form, router, tournamentId]);

  if (loading) return <FullscreenLoader />;

  return (
    <NavLayout title="Tournament Rules">
      <VStack className="px-6 pb-6 pt-6 space-y-6">
        {config.map((field) => (
          <RuleFieldWrapper
            key={`${field.key}-${screenMode}`}
            field={field}
            screenMode={screenMode}
            formValue={form[field.key] ?? ''}
            error={errors[field.key]}
            onChange={handleChange}
          />
        ))}

        <Button onPress={handleSubmit} className="mt-6 bg-primary-0">
          <ButtonText className="font-urbanistSemiBold">Save Rules</ButtonText>
        </Button>
      </VStack>
    </NavLayout>
  );
}
