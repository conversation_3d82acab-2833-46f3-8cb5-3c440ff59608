import { useRouter, useLocalSearchParams } from 'expo-router';
import { NavLayout } from '@/components/NavLayout';
import PairForm from '@/pages/Pairs/PairForm';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import { useTournamentById } from '@/hooks/useTournamentById';
import { toast } from '@/toast/toast';
import SCREENS from '@/constants/Screens';
import { useCallback } from 'react';

export default function CreatePairModal() {
  const router = useRouter();
  const { 'tournament-id': tournamentId } = useLocalSearchParams();
  const { tournament, loading } = useTournamentById(tournamentId as string);

  const handleSubmit = useCallback(
    (teamId: string) => {
      toast.success('Pair created successfully');
      router.replace({
        pathname: SCREENS.PAIR_VIEW,
        params: {
          'pair-id': teamId,
          'tournament-id': tournamentId,
        },
      });
    },
    [router, tournamentId]
  );

  return (
    <NavLayout title="Create Pair" noScroll>
      {loading ? (
        <FullscreenLoader />
      ) : tournament ? (
        <PairForm
          tournament={tournament}
          onSubmit={handleSubmit}
          submitButtonText="Create Pair"
          isLoading={false}
        />
      ) : null}
    </NavLayout>
  );
}
