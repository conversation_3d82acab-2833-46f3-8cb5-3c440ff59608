import { useRouter, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { useCallback, useState } from 'react';
import { NavLayout } from '@/components/NavLayout';
import PairForm from '@/pages/Pairs/PairForm';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import { type Pair, getInitialPairFormData } from '@/types/pairs';
import { fetchTeamById } from '@/services/teamsService';
import { toast } from '@/toast/toast';
import { useTournamentById } from '@/hooks/useTournamentById';
import { useTeamPlayers } from '@/hooks/useTeamPlayers';

export default function EditPairScreen() {
  const router = useRouter();
  const { 'pair-id': pairId, 'tournament-id': tournamentId } =
    useLocalSearchParams();

  const [pair, setPair] = useState<Pair | null>(null);
  const [loading, setLoading] = useState(true);

  const { tournament } = useTournamentById(tournamentId as string);

  // Fetch team players for the pair
  const { players: pairPlayers, loading: playersLoading } = useTeamPlayers({
    tournamentId: tournamentId as string,
    teamId: pairId as string,
    autoFetch: !!pairId,
  });

  const loadPair = useCallback(async () => {
    if (!pairId) return;

    setLoading(true);
    try {
      const {
        success,
        team: fetchedPair,
        error,
      } = await fetchTeamById(pairId as string);

      if (!success) {
        toast.error(error || 'Failed to load pair details');
        router.back();
        return;
      }

      const pairData: Pair = {
        ...fetchedPair,
        player1: fetchedPair.player1,
        player2: fetchedPair.player2,
      };

      setPair(pairData);
    } catch (error) {
      toast.error('Something went wrong');
      router.back();
    } finally {
      setLoading(false);
    }
  }, [pairId, router]);

  useFocusEffect(
    useCallback(() => {
      loadPair();
    }, [loadPair])
  );

  const handleSubmit = async (_teamId: string) => {
    toast.success('Pair updated successfully');
    router.back();
  };

  const isLoading = loading || playersLoading;
  const initialFormData = pair ? getInitialPairFormData(pair) : undefined;

  return (
    <NavLayout title="Edit Pair">
      {isLoading ? (
        <FullscreenLoader />
      ) : pair && tournament ? (
        <PairForm
          pairId={pairId as string}
          tournament={tournament}
          onSubmit={handleSubmit}
          submitButtonText="Update Pair"
          isLoading={false}
          initialFormData={initialFormData}
          initialPlayers={pairPlayers}
        />
      ) : null}
    </NavLayout>
  );
}
