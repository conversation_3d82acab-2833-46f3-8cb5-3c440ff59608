import { useLocalSearchParams, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import { NavLayout } from '@/components/NavLayout';
import PairPage from '@/pages/Pairs/PairPage';
import { type Pair } from '@/types/pairs';
import { CustomMenu, type MenuOption } from '@/components/CustomMenu';
import { EditIcon, TrashIcon } from '@/components/ui/icon';
import TeamDeleteConfirmationModal from '../../../../pages/Teams/components/TeamDeleteConfirmationModal';
import SCREENS from '@/constants/Screens';
import { useTeamPlayers } from '@/hooks/useTeamPlayers';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import { useTournamentTeam } from '@/pages/Teams/hooks/useTournamentTeam';
import { Edit2Icon } from 'lucide-react-native';

export default function PairViewScreen() {
  const router = useRouter();
  const { 'pair-id': pairId, 'tournament-id': tournamentId } =
    useLocalSearchParams();
  const { players, loading: playersLoading } = useTeamPlayers({
    tournamentId: tournamentId as string,
    teamId: pairId as string,
  });
  const { team: pairDetails, loading: pairDetailsLoading } = useTournamentTeam({
    teamId: pairId as string,
  });
  const [pair, setPair] = useState<Pair | null>(null);

  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const menuOptions: MenuOption[] = [
    {
      key: 'edit-pair',
      label: 'Edit Pair',
      icon: EditIcon,
      onPress: () => {
        router.push({
          pathname: SCREENS.PAIR_EDIT,
          params: {
            'pair-id': pairId,
            'tournament-id': tournamentId,
          },
        });
      },
    },
    {
      key: 'edit-pair-info',
      label: 'Edit Pair Info',
      icon: Edit2Icon,
      onPress: () => {
        router.push({
          pathname: SCREENS.TEAM_INFO,
          params: {
            'team-id': pairId,
          },
        });
      },
      separatorBefore: true,
    },
    {
      key: 'delete-pair',
      label: 'Delete Pair',
      icon: TrashIcon,
      onPress: () => setShowDeleteModal(true),
      optionClassName: 'text-red-500',
      separatorBefore: true,
    },
  ];

  useEffect(() => {
    if (players.length >= 0 && pairDetails) {
      const pairData: Pair = {
        tournament_id: tournamentId as string,
        ...pairDetails,
        player1: players[0] || undefined,
        player2: players[1] || undefined,
      };
      setPair(pairData);
    }
  }, [players, pairId, tournamentId, pairDetails]);

  return (
    <>
      <NavLayout
        title={'Pair Details'}
        isFullscreen
        right={
          pair && (
            <CustomMenu
              options={menuOptions}
              placement="right top"
              offset={-20}
            />
          )
        }
      >
        {playersLoading || (pairDetailsLoading && <FullscreenLoader />)}
        {pair && <PairPage pair={pair} />}
      </NavLayout>

      <TeamDeleteConfirmationModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        team={pair}
      />
    </>
  );
}
