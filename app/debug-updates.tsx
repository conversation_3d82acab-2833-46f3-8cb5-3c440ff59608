import React from 'react';
import { Safe<PERSON>reaView, Sc<PERSON>View } from 'react-native';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/button';
import { router } from 'expo-router';
import UpdateDebugger from '@/components/UpdateDebugger';
import { UpdatesManager } from '@/components/UpdatesManager';

export default function DebugUpdatesScreen() {
  return (
    <SafeAreaView style={{ flex: 1 }} className="mt-10">
      <ScrollView className=" bg-white">
        <Box className="p-4">
          {/* Header */}
          <Box className="flex-row items-center justify-between mb-6">
            <Text className="text-2xl font-bold">Updates Debug</Text>
            <Button variant="outline" size="sm" onPress={() => router.back()}>
              <ButtonText>Back</ButtonText>
            </Button>
          </Box>

          {/* Test Content - Change this to test updates */}
          <Box className="mb-6 p-4 bg-green-50 rounded-lg">
            <Text className="font-semibold mb-2">🎯 Test Content:</Text>
            <Text className="text-lg">
              Version: 0.1.1 - Updated 7/12/2025, 10:18:51 PM
            </Text>
            <Text className="text-sm text-gray-600">
              Change this text and publish an update to see it change!!!
            </Text>
          </Box>

          {/* Update Debugger */}
          <UpdateDebugger showInProduction={true} />

          {/* Manual Updates Manager */}
          <Box className="mt-6">
            <Text className="font-semibold mb-3">Manual Update Manager:</Text>
            <UpdatesManager showDebugInfo={true} />
          </Box>

          {/* Console Logs Info */}
          <Box className="mt-6 p-4 bg-gray-50 rounded-lg">
            <Text className="font-semibold mb-2">📱 Monitoring Tips:</Text>
            <Text className="text-sm mb-1">
              • Check console logs for background update activity
            </Text>
            <Text className="text-sm mb-1">
              • Background updates happen every 30 minutes
            </Text>
            <Text className="text-sm mb-1">
              • Updates install when app goes to background
            </Text>
            <Text className="text-sm">
              • Force check using the button above
            </Text>
          </Box>
        </Box>
      </ScrollView>
    </SafeAreaView>
  );
}
