import { Stack } from 'expo-router';
import { Animated } from 'react-native';
import { useState } from 'react';
import { GluestackUIProvider } from '@/components/ui/gluestack-ui-provider';
import AnimatedSplash from '@/components/SplashScreen';
import { useLoadFonts } from '@/hooks/useLoadFonts';
import { ToastProvider } from '@gluestack-ui/toast';
import { RecoilRoot } from 'recoil';
import { useAuthUser } from '@/hooks/useAuthUser';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import { BackgroundUpdater } from '@/components/SilentUpdatesManager';
import '../global.css';
import { StatusBar } from 'expo-status-bar';

export { ErrorBoundary } from 'expo-router';
function AppLayout() {
  const { fontsLoaded } = useLoadFonts();
  const { loading: authLoading } = useAuthUser();
  const [showSplash, setShowSplash] = useState(true);

  const handleSplashComplete = () => {
    setShowSplash(false); // Hide splash screen after animation
  };

  if (authLoading) {
    return <FullscreenLoader />;
  }

  return showSplash ? (
    <AnimatedSplash onAnimationComplete={handleSplashComplete} />
  ) : (
    <Animated.View style={{ flex: 1 }}>
      <StatusBar
        style="dark" // dark-content
        translucent
        backgroundColor="transparent"
      />
      <Animated.View style={{ flex: 1 }}>
        <RecoilRoot>
          <GluestackUIProvider
            mode="light"
            // @ts-ignore
            config={{
              theme: {
                fontConfig: {
                  body: 'Urbanist_400Regular',
                  heading: 'Urbanist_600SemiBold',
                },
              },
            }}
          >
            <BackgroundUpdater checkIntervalMinutes={30} />
            <Stack
              screenOptions={{
                animation: 'slide_from_right',
                headerShown: false,
              }}
            />
          </GluestackUIProvider>
        </RecoilRoot>
      </Animated.View>
    </Animated.View>
  );
}

export default function RootLayout() {
  return (
    <ToastProvider>
      <AppLayout />
    </ToastProvider>
  );
}
