import React from 'react';
import {
  <PERSON>u,
  <PERSON>uItem,
  MenuItem<PERSON>abel,
  MenuSeparator,
} from '@/components/ui/menu';
import { Button, ButtonIcon } from '@/components/ui/button';
import { Icon } from '@/components/ui/icon';
import { EllipsisVerticalIcon } from 'lucide-react-native';

export type MenuOption = {
  key: string;
  label: string;
  icon?: any;
  onPress?: () => void;
  separatorBefore?: boolean;
  optionClassName?: string;
};

type CustomMenuProps = {
  triggerText?: string;
  options: MenuOption[];
  placement?:
    | 'bottom'
    | 'top'
    | 'right'
    | 'left'
    | 'top left'
    | 'top right'
    | 'bottom left'
    | 'bottom right'
    | 'right top'
    | 'right bottom'
    | 'left top'
    | 'left bottom';
  offset?: number;
  disabledKeys?: string[];
  closeOnSelect?: boolean;
};

export const CustomMenu: React.FC<CustomMenuProps> = ({
  options,
  placement = 'bottom',
  offset = 0,
  disabledKeys = [],
  closeOnSelect = true,
}) => {
  return (
    <Menu
      placement={placement}
      offset={offset}
      disabledKeys={disabledKeys}
      closeOnSelect={closeOnSelect}
      className="shadow-xl shadow-black/20"
      trigger={({ ...triggerProps }) => {
        return (
          <Button className="px-1 py-1" variant="link" {...triggerProps}>
            {/* @ts-ignore */}
            <ButtonIcon as={EllipsisVerticalIcon} size="2xl"></ButtonIcon>
          </Button>
        );
      }}
    >
      {options.map((item) => (
        <React.Fragment key={item.key}>
          {item.separatorBefore && <MenuSeparator />}
          <MenuItem
            key={item.key}
            textValue={item.label}
            onPress={item.onPress}
          >
            {item.icon && (
              <Icon
                as={item.icon}
                size="sm"
                className={`mr-2 shrink-0 ${item.optionClassName}`}
              />
            )}
            <MenuItemLabel
              className={`font-urbanistSemiBold text-sm ${item.optionClassName}`}
            >
              {item.label}
            </MenuItemLabel>
          </MenuItem>
        </React.Fragment>
      ))}
    </Menu>
  );
};
