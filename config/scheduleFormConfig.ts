import { FieldType } from '@/components/k-components/FormField';
import type { EditableFieldConfig } from '@/components/k-components/EditableFormField';
import {
  SingleEliminationIcon,
  RoundRobinIcon,
  GroupStageIcon,
  CustomIcon,
  DoubleEliminationIcon,
} from '@/components/ui/icon';

export const TOURNAMENT_FORMATS = {
  SINGLE_ELIMINATION: 'single_elimination',
  DOUBLE_ELIMINATION: 'double_elimination',
  ROUND_ROBIN: 'round_robin',
  GROUP_STAGE: 'group_stage',
  CUSTOM: 'custom',
} as const;

export const TOURNAMENT_FORMAT_LABELS = {
  [TOURNAMENT_FORMATS.SINGLE_ELIMINATION]: 'Single Elimination',
  [TOURNAMENT_FORMATS.DOUBLE_ELIMINATION]: 'Double Elimination',
  [TOURNAMENT_FORMATS.ROUND_ROBIN]: 'Round Robin',
  [TOURNAMENT_FORMATS.GROUP_STAGE]: 'Group Stage',
  [TOURNAMENT_FORMATS.CUSTOM]: 'Custom',
} as const;

export const DETAILED_TOURNAMENT_FORMAT_OPTIONS = [
  {
    label: TOURNAMENT_FORMAT_LABELS[TOURNAMENT_FORMATS.SINGLE_ELIMINATION],
    value: TOURNAMENT_FORMATS.SINGLE_ELIMINATION,
    description:
      'Teams are eliminated after losing one match. Fast and decisive format. (Coming Soon)',
    icon: SingleEliminationIcon,
    disabled: true,
  },
  {
    label: TOURNAMENT_FORMAT_LABELS[TOURNAMENT_FORMATS.DOUBLE_ELIMINATION],
    value: TOURNAMENT_FORMATS.DOUBLE_ELIMINATION,
    description:
      'Teams get a second chance. Eliminated only after losing twice. (Coming Soon)',
    icon: DoubleEliminationIcon,
    disabled: true,
  },
  {
    label: TOURNAMENT_FORMAT_LABELS[TOURNAMENT_FORMATS.ROUND_ROBIN],
    value: TOURNAMENT_FORMATS.ROUND_ROBIN,
    description:
      'Every team plays every other team. Most comprehensive format. (Coming Soon)',
    icon: RoundRobinIcon,
      disabled: true,
  },
  {
    label: TOURNAMENT_FORMAT_LABELS[TOURNAMENT_FORMATS.GROUP_STAGE],
    value: TOURNAMENT_FORMATS.GROUP_STAGE,
    description:
      'Teams divided into groups, then top teams advance to knockout. (Coming Soon)',
    icon: GroupStageIcon,
    disabled: true,
  },
  {
    label: TOURNAMENT_FORMAT_LABELS[TOURNAMENT_FORMATS.CUSTOM],
    value: TOURNAMENT_FORMATS.CUSTOM,
    description:
      'Create your own unique tournament structure and rules. Create matches on the go.',
    icon: CustomIcon,
  },
];

export const validateTournamentFormat = (value: string): string | null => {
  if (!value || value.trim() === '') {
    return 'Tournament format is required';
  }

  const validFormats = Object.values(TOURNAMENT_FORMATS);
  if (!validFormats.includes(value as any)) {
    return 'Please select a valid tournament format';
  }

  return null;
};

export const scheduleFormFields: EditableFieldConfig[] = [
  {
    key: 'format',
    type: FieldType.DETAILED_SELECT,
    label: 'Tournament Format',
    required: true,
    options: DETAILED_TOURNAMENT_FORMAT_OPTIONS,
    validators: [validateTournamentFormat],
    info: 'Choose the format that best suits your tournament structure',
    editable: {
      type: 'warning',
      message:
        'Changing the tournament format will reset all existing matches and brackets. This action cannot be undone. Are you sure you want to continue?',
    },
  },
];

export const getTournamentFormatLabel = (format: string): string => {
  return (
    TOURNAMENT_FORMAT_LABELS[format as keyof typeof TOURNAMENT_FORMAT_LABELS] ||
    format
  );
};

export type TournamentFormat =
  (typeof TOURNAMENT_FORMATS)[keyof typeof TOURNAMENT_FORMATS];

export interface ScheduleFormData {
  format?: TournamentFormat;
}

export const getInitialScheduleFormData = (
  schedule?: Partial<ScheduleFormData>
): ScheduleFormData => ({
  format: schedule?.format,
});
