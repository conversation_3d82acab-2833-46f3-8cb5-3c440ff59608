import { fetchUserTournaments } from '@/services/tournamentService';
import { fetchPlayers, fetchPlayersForPairs } from '@/services/playerService';
import { fetchTeams, fetchPairs } from '@/services/teamsService';
import EventCard from '@/components/k-components/EventCard';
import { PlayerListCard } from '@/pages/Players/components/PlayerListCard';
import { TeamListCard } from '@/pages/Teams/components/TeamListCard';
import PairListCard from '@/pages/Pairs/components/PairListCard';
import { getSportLabel } from '@/utils/sports-utils';
import SCREENS from '@/constants/Screens';

export interface ViewAllConfigItem<T> {
  title: string;
  fetchData: (
    query: string,
    limit?: number,
    extraParams?: Record<string, any>
  ) => Promise<{
    data: T[];
    totalCount?: number;
  }>;
  renderCard: (item: T, extraParams?: Record<string, any>) => JSX.Element;
}

export interface ViewAllConfigMap {
  [key: string]: ViewAllConfigItem<any>;
}

export const VIEW_ALL_CONFIG: ViewAllConfigMap = {
  tournament: {
    title: 'Your Tournaments',
    fetchData: async (query, limit = 10, extraParams = {}) => {
      const { page = 1 } = extraParams;

      const res = await fetchUserTournaments({
        search: query,
        limit,
        page,
      });

      return {
        data: res.data || [],
        totalCount: res.count ?? 0,
      };
    },
    renderCard: (item) => (
      <EventCard
        key={item.id}
        name={item.name}
        logo={item.logo_url}
        venue={item.venue}
        type={getSportLabel(item.sport_type)}
        status={item.status}
        routeOnPress={{
          pathname: SCREENS.TOURNAMENT_VIEW,
          params: {
            'tournament-id': item.id,
          },
        }}
      />
    ),
  },
  players: {
    title: 'Players',
    fetchData: async (query, limit = 10, extraParams = {}) => {
      const { tournamentId, page = 1 } = extraParams;
      const res = await fetchPlayers({
        tournamentId,
        search: query,
        page,
        limit,
      });

      return {
        data: res.players,
        totalCount: res.count,
      };
    },
    renderCard: (item) => <PlayerListCard key={item.id} player={item} />,
  },
  teams: {
    title: 'Teams',
    fetchData: async (query, limit = 10, extraParams = {}) => {
      const { tournamentId, page = 1 } = extraParams;
      const res = await fetchTeams({
        tournamentId,
        search: query,
        page,
        limit,
      });

      return {
        data: res.teams,
        totalCount: res.count,
      };
    },
    renderCard: (item, extraParams) => (
      <TeamListCard
        key={item.id}
        team={item}
        tournamentId={extraParams?.tournamentId || item.tournament_id || ''}
      />
    ),
  },
  pairs: {
    title: 'Pairs',
    fetchData: async (query, limit = 10, extraParams = {}) => {
      const { tournamentId, page = 1 } = extraParams;
      const res = await fetchPairs({
        tournamentId,
        search: query,
        page,
        limit,
      });

      if (res.error || !res.teams || res.teams.length === 0) {
        return {
          data: res.teams || [],
          totalCount: res.count || 0,
        };
      }

      const pairIds = res.teams.map((pair: any) => pair.id);
      const { playersByPairId, error: playersError } =
        await fetchPlayersForPairs({
          tournamentId,
          pairIds,
        });

      if (playersError && __DEV__) {
        console.warn('Some players failed to load in ViewAll:', playersError);
      }

      const pairsWithPlayers = res.teams.map((pair: any) => {
        const players = playersByPairId[pair.id] || [];
        return {
          ...pair,
          player1: players[0] || null,
          player2: players[1] || null,
        };
      });

      return {
        data: pairsWithPlayers,
        totalCount: res.count || 0,
      };
    },
    renderCard: (item, extraParams) => (
      <PairListCard
        key={item.id}
        pair={item}
        tournamentId={extraParams?.tournamentId || item.tournament_id || ''}
      />
    ),
  },
};
