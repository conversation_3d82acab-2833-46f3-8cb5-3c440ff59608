import {
  FieldType,
  type FieldConfig,
} from '@/components/k-components/FormField';
import { validateEmail, validatePhone } from '@/utils';

export const teamInfoFormFields: FieldConfig[] = [
  {
    key: 'tagline',
    type: FieldType.TEXT,
    label: 'Team Tagline',
    required: false,
    info: 'Add a motivational tagline or motto that represents your team spirit',
    inputProps: {
      placeholder: 'Enter team tagline or motto',
      multiline: true,
      numberOfLines: 3,
    },
  },
];

export const teamInfoSectionFields: FieldConfig[] = [
  {
    key: 'coach_name',
    type: FieldType.TEXT,
    label: 'Coach Name',
    required: false,
    info: 'Enter the name of the team coach or manager',
    inputProps: {
      placeholder: 'Enter coach name',
    },
  },
  {
    key: 'owner_name',
    type: FieldType.TEXT,
    label: 'Team Owner',
    required: false,
    info: 'Enter the name of the team owner',
    inputProps: {
      placeholder: 'Enter team owner name',
    },
  },
];

export const teamContactSectionFields: FieldConfig[] = [
  {
    key: 'team_phone',
    type: FieldType.TEXT,
    label: 'Phone Number',
    required: false,
    validators: [validatePhone],
    info: 'Team contact phone number for communication',
    inputProps: {
      placeholder: 'Enter phone number',
      keyboardType: 'phone-pad',
    },
  },
  {
    key: 'team_email',
    type: FieldType.TEXT,
    label: 'Team Email',
    required: false,
    validators: [validateEmail],
    info: 'Official team email address for correspondence',
    inputProps: {
      placeholder: 'Enter team email',
      keyboardType: 'email-address',
      autoCapitalize: 'none',
    },
  },
];

export interface TeamInfoFormData {
  tagline: string;
  coach_name: string;
  owner_name: string;
  team_phone: string;
  team_email: string;
}

export const getInitialTeamInfoFormData = (team?: any): TeamInfoFormData => ({
  tagline: team?.tagline || '',
  coach_name: team?.coach_name || '',
  owner_name: team?.owner_name || '',
  team_phone: team?.team_phone || '',
  team_email: team?.team_email || '',
});
