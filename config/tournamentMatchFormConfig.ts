import {
  FieldType,
  type FieldConfig,
} from '@/components/k-components/FormField';
import type { Option } from '@/components/k-components/AsyncSelectWithSearch';

export interface TournamentMatchFormData {
  teamA?: Option | null;
  teamB?: Option | null;
  scheduled_date?: string | null;
  stage?: Option | null;
  court_field_number?: Option | null;
}

export const getInitialTournamentMatchFormData = (
  match?: Partial<TournamentMatchFormData>
): TournamentMatchFormData => ({
  teamA: match?.teamA,
  teamB: match?.teamB,
  scheduled_date: match?.scheduled_date,
  stage: match?.stage,
  court_field_number: match?.court_field_number,
});

export const validateParticipantSelection = (
  value: Option | undefined,
  fieldName: string
): string | null => {
  if (!value) {
    return `${fieldName} is required`;
  }
  return null;
};

export const createTournamentMatchFormFields = (
  roundOptions: Option[],
  courtFieldOptions: Option[],
  onRoundCreate: (label: string) => Promise<{ label: string; value: string }>,
  onCourtFieldCreate: (
    label: string
  ) => Promise<{ label: string; value: string }>,
  tournamentStartDate?: string | null,
  tournamentEndDate?: string | null
): FieldConfig[] => [
  {
    key: 'scheduled_date',
    type: FieldType.DATETIME_PICKER,
    label: 'Match Date & Time',
    required: true,
    info:
      tournamentStartDate && tournamentEndDate
        ? `Schedule matches between ${new Date(
            tournamentStartDate
          ).toLocaleDateString()} and ${new Date(
            tournamentEndDate
          ).toLocaleDateString()}`
        : 'Schedule when this match will be played',
    dateTimeMode: 'both',
    minDate: tournamentStartDate ? new Date(tournamentStartDate) : undefined,
    maxDate: tournamentEndDate ? new Date(tournamentEndDate) : undefined,
    inputProps: {
      placeholder: 'Select match date & time',
    },
  },
  {
    key: 'stage',
    type: FieldType.CREATABLE_SELECT,
    label: 'Match Round',
    required: true,
    info: 'Select the round for this match. Formats like "Semi-Final", "Final" and more are supported.',
    options: roundOptions,
    onCreateOption: async (label: string) => {
      const newOption = await onRoundCreate(label);
      return {
        label: newOption.label,
        value: newOption.value,
      };
    },
    createOptionText: 'Create Round',
    searchPlaceholder: 'Search or type to create round...',
  },
  {
    key: 'court_field_number',
    type: FieldType.CREATABLE_SELECT,
    label: 'Court/Field',
    required: false,
    info: 'Select the court or field where this match will be played',
    options: courtFieldOptions,
    onCreateOption: async (label: string) => {
      const newOption = await onCourtFieldCreate(label);
      return {
        label: newOption.label,
        value: newOption.value,
      };
    },
    createOptionText: 'Create Court/Field',
    searchPlaceholder: 'Search or type to create court/field...',
  },
];
