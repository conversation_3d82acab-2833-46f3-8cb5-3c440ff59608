export interface RoundOption {
  key: string;
  name: string;
  priority: number;
}

export const ROUND_CONFIG: RoundOption[] = [
  // League & Group Stage (lowest priority)
  { key: 'group_stage', name: 'Group Stage', priority: 1 },
  { key: 'round_1', name: 'Round 1', priority: 2 },
  { key: 'round_2', name: 'Round 2', priority: 3 },
  { key: 'round_3', name: 'Round 3', priority: 4 },
  { key: 'round_4', name: 'Round 4', priority: 5 },
  { key: 'round_5', name: 'Round 5', priority: 6 },

  // Qualifiers
  { key: 'qualifier_1', name: 'Qualifier 1', priority: 7 },
  { key: 'qualifier_2', name: 'Qualifier 2', priority: 8 },
  { key: 'qualifier_3', name: 'Qualifier 3', priority: 9 },
  { key: 'qualifier_4', name: 'Qualifier 4', priority: 10 },
  { key: 'eliminator', name: 'Eliminator', priority: 11 },

  // Knockout Rounds
  { key: 'round_of_64', name: 'Round of 64', priority: 12 },
  { key: 'round_of_32', name: 'Round of 32', priority: 13 },
  { key: 'round_of_16', name: 'Round of 16', priority: 14 },
  { key: 'pre_quarter_final', name: 'Pre-Quarter Final', priority: 15 },
  { key: 'quarter_final', name: 'Quarter Final', priority: 16 },
  { key: 'semi_final', name: 'Semi Final', priority: 17 },

  // Placement Matches
  { key: 'third_place_playoff', name: '3rd Place Playoff', priority: 18 },
  { key: 'fifth_place_match', name: '5th Place Match', priority: 19 },

  // Misc Matches
  { key: 'friendly', name: 'Friendly', priority: 20 },
  { key: 'exhibition_match', name: 'Exhibition Match', priority: 21 },
  { key: 'practice_match', name: 'Practice Match', priority: 22 },

  // Final (always last)
  { key: 'final', name: 'Final', priority: 100 },
];

export const getRoundOptions = () => {
  return ROUND_CONFIG.map((round) => ({
    label: round.name,
    value: round.key,
  }));
};

export const findRoundByKey = (key: string): RoundOption | undefined => {
  return ROUND_CONFIG.find((round) => round.key === key);
};
