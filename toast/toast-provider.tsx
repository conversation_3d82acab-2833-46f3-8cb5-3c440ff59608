import React, { useState, useCallback, useEffect } from 'react';
import { View, Text, Animated, StyleSheet } from 'react-native';
import { setToastFunction } from './toast';
import type { ToastType } from './toast';
import { CircleCheck, AlertTriangle, Info } from 'lucide-react-native';
import { BlurView } from 'expo-blur';

export const ToastProvider = ({ children }: { children: React.ReactNode }) => {
  const [visible, setVisible] = useState(false);
  const [message, setMessage] = useState('');
  const [type, setType] = useState<ToastType>('info');
  const [translateY] = useState(new Animated.Value(60));
  const [opacity] = useState(new Animated.Value(0));

  const showToast = useCallback((msg: string, t: ToastType = 'info') => {
    setMessage(msg);
    setType(t);
    setVisible(true);

    Animated.parallel([
      Animated.timing(opacity, {
        toValue: 1,
        duration: 350,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: 0,
        duration: 350,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setTimeout(() => {
        Animated.parallel([
          Animated.timing(opacity, {
            toValue: 0,
            duration: 350,
            useNativeDriver: true,
          }),
          Animated.timing(translateY, {
            toValue: 60,
            duration: 350,
            useNativeDriver: true,
          }),
        ]).start(() => {
          setVisible(false);
        });
      }, 2500);
    });
  }, []);

  useEffect(() => {
    setToastFunction(showToast);
  }, [showToast]);

  const backgroundColors: Record<ToastType, string> = {
    success: '#1DB954',
    error: '#ef4444',
    info: '#3b82f6',
  };

  const backgroundColorWithOpacity = `${backgroundColors[type]}22`;

  const iconMap: Record<ToastType, React.ReactNode> = {
    success: (
      <CircleCheck
        size={18}
        color={backgroundColors[type]}
        style={{ marginRight: 8 }}
      />
    ),
    error: (
      <AlertTriangle
        size={18}
        color={backgroundColors[type]}
        style={{ marginRight: 8 }}
      />
    ),
    info: (
      <Info
        size={18}
        color={backgroundColors[type]}
        style={{ marginRight: 8 }}
      />
    ),
  };

  return (
    <>
      {children}
      {visible && (
        <Animated.View
          style={[
            styles.toastWrapper,
            {
              transform: [{ translateY }],
            },
          ]}
        >
          <BlurView
            intensity={60}
            experimentalBlurMethod="dimezisBlurView"
            tint="light"
            style={[
              styles.toast,
              {
                borderColor: backgroundColors[type],
                backgroundColor: `${backgroundColors[type]}22`,
              },
            ]}
          >
            <View className="flex flex-row gap-2 items-center py-4 px-5">
              {iconMap[type]}
              <Text
                style={{ color: backgroundColors[type] }}
                className="font-urbanistSemiBold pr-2"
              >
                {message}
              </Text>
            </View>
          </BlurView>
        </Animated.View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  toast: {
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
  },
  toastWrapper: {
    position: 'absolute',
    bottom: 60,
    left: 20,
    right: 20,
    zIndex: 9999,
  },
  toastContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  toastText: {
    color: 'white',
    fontSize: 15,
    fontFamily: 'Urbanist_600SemiBold',
    flex: 1,
  },
});
