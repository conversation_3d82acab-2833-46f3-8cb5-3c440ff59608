# Expo Updates Setup

This document explains how to use Expo Updates in your Kali app to push over-the-air updates without going through app stores.

## Overview

Expo Updates allows you to push JavaScript and asset updates directly to your users' devices. This is perfect for:

- Bug fixes
- Feature updates that don't require native code changes
- Content updates
- Configuration changes

## Configuration

### Channels

We have three update channels configured:

- **development**: For testing updates during development
- **preview**: For internal testing and staging
- **production**: For live app updates

### Runtime Version

We're using `appVersion` policy, which means updates are compatible with apps that have the same version number in `app.json`.

## Publishing Updates

### Method 1: Interactive Script (Recommended)

```bash
yarn publish-update
```

This will guide you through:

1. Selecting a channel
2. Entering an update message
3. Confirming the update

### Method 2: Direct Commands

```bash
# Development channel
yarn update:development "Your update message"

# Preview channel
yarn update:preview "Your update message"

# Production channel
yarn update:production "Your update message"
```

### Method 3: EAS CLI Directly

```bash
eas update --branch production --message "Your update message"
```

## Using Updates in Your App

### Silent Background Updates (Recommended)

```tsx
import { BackgroundUpdater } from '@/components/SilentUpdatesManager';

function App() {
  return (
    <>
      <BackgroundUpdater checkIntervalMinutes={30} />
      <YourAppContent />
    </>
  );
}
```

### Update Strategies

1. **Auto Updates** (`AutoUpdater`):

   - Downloads and installs immediately
   - App restarts automatically when update is available
   - Most aggressive, use for critical updates

2. **Background Updates** (`BackgroundUpdater`):

   - Downloads silently in background
   - Installs when app goes to background
   - Good balance of automatic updates and user experience

3. **Silent Downloads** (`SilentDownloader`):
   - Downloads silently in background
   - Installs on next app restart
   - Most conservative, user controls when updates apply

### Manual Updates (with prompts)

```tsx
import { UpdatesManager } from '@/components/UpdatesManager';

function SettingsScreen() {
  return (
    <View>
      {/* Your other settings */}
      <UpdatesManager />
    </View>
  );
}
```

### Custom Implementation

```tsx
import { useUpdates } from '@/lib/hooks/useUpdates';

function MyComponent() {
  const { isChecking, updateInfo, checkForUpdates, downloadAndInstall } =
    useUpdates();

  return (
    <View>
      <Button onPress={checkForUpdates} disabled={isChecking}>
        {isChecking ? 'Checking...' : 'Check for Updates'}
      </Button>

      {updateInfo?.isUpdateAvailable && (
        <Button onPress={downloadAndInstall}>Download Update</Button>
      )}
    </View>
  );
}
```

### Background Updates

```tsx
import { useBackgroundUpdates } from '@/lib/hooks/useUpdates';

function App() {
  const { hasUpdate, applyUpdate } = useBackgroundUpdates();

  // Show notification when update is ready
  useEffect(() => {
    if (hasUpdate) {
      Alert.alert('Update Ready', 'Restart to apply the latest update?', [
        { text: 'Later', style: 'cancel' },
        { text: 'Restart', onPress: applyUpdate },
      ]);
    }
  }, [hasUpdate]);

  return <YourApp />;
}
```

### Advanced Silent Updates

```tsx
import {
  useAutoSilentUpdates,
  useSilentDownloadUpdates,
} from '@/lib/hooks/useUpdates';

function MyApp() {
  // Option 1: Completely automatic (restarts app immediately)
  useAutoSilentUpdates({
    checkInterval: 30 * 60 * 1000, // 30 minutes
    onUpdateStart: () => console.log('Starting auto-update...'),
    onUpdateComplete: () => console.log('Update complete, restarting...'),
    onUpdateError: (error) => console.error('Update failed:', error),
  });

  // Option 2: Silent download with manual install
  const { updateReady, installUpdate } = useSilentDownloadUpdates({
    checkInterval: 30 * 60 * 1000,
    onUpdateDownloaded: () => console.log('Update downloaded and ready'),
  });

  // Install update when app goes to background
  useEffect(() => {
    const handleAppStateChange = (nextAppState) => {
      if (nextAppState === 'background' && updateReady) {
        installUpdate();
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange
    );
    return () => subscription?.remove();
  }, [updateReady, installUpdate]);

  return <YourApp />;
}
```

### Integration with Existing App Structure

```tsx
// In your main App component or _layout.tsx
import { BackgroundUpdater } from '@/components/SilentUpdatesManager';

export default function RootLayout() {
  return (
    <ToastProvider>
      <RecoilRoot>
        <GluestackUIProvider>
          {/* Add the updater here - it runs invisibly in background */}
          <BackgroundUpdater
            checkIntervalMinutes={30}
            enableUpdateLogging={__DEV__}
          />

          <Stack screenOptions={{ headerShown: false }} />
        </GluestackUIProvider>
      </RecoilRoot>
    </ToastProvider>
  );
}
```

## Best Practices

### 1. Update Timing

- Check for updates when the app becomes active
- Download updates in the background
- Apply updates when the app is restarted or at natural break points

### 2. User Experience

- Always inform users about updates
- Provide option to update later
- Show progress during downloads
- Handle update failures gracefully

### 3. Testing

- Test updates on the preview channel before production
- Verify updates work on different devices and OS versions
- Have a rollback plan for problematic updates

### 4. Update Messages

Use clear, descriptive messages:

```bash
# Good
"Fix crash when uploading team photos"
"Add new tournament bracket view"
"Update player statistics calculation"

# Bad
"Bug fixes"
"Updates"
"v1.2.3"
```

## Monitoring Updates

### Check Current Update Info

```tsx
import { getCurrentUpdateInfo } from '@/lib/updates';

const info = getCurrentUpdateInfo();
console.log('Update ID:', info.updateId);
console.log('Channel:', info.channel);
console.log('Runtime Version:', info.runtimeVersion);
```

### Debug Mode

Add the UpdatesManager with debug info in development:

```tsx
<UpdatesManager showDebugInfo={__DEV__} />
```

## Troubleshooting

### Updates Not Working

1. Check that you're not in development mode (`__DEV__` is false)
2. Verify the runtime version matches between app and update
3. Ensure the update was published to the correct channel
4. Check network connectivity

### Update Fails to Apply

1. Check console for error messages
2. Verify app has sufficient storage
3. Try clearing app cache
4. Check if update is compatible with current app version

### Emergency Rollback

If an update causes issues:

1. Publish a new update that fixes the problem
2. Or publish a rollback update with previous working code
3. Users will get the fix on next update check

## Commands Reference

```bash
# Check EAS CLI is installed
eas --version

# Login to EAS (if not already)
eas login

# Publish update with interactive script
yarn publish-update

# Publish to specific channel
yarn update:production "Your message"

# Check update status
eas update:list --branch production

# View update details
eas update:view [UPDATE_ID]
```

## Security Notes

- Updates are signed and verified by Expo
- Only JavaScript and assets can be updated (no native code)
- Updates respect the same permissions as the original app
- Runtime version policy prevents incompatible updates

## Limitations

- Cannot update native code (requires new app store release)
- Cannot change app permissions
- Cannot update Expo SDK version
- Updates must be compatible with the current runtime version
