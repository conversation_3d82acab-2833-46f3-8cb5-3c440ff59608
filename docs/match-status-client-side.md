# Client-Side Match Status Management

## Overview

Match status is managed with a hybrid approach:
- **Backend**: Stores only 4 core statuses in the database
- **Frontend**: Dynamically calculates "delayed" status for display

## Database Status Enum

The `status` column in the `matches` table uses an enum with exactly 4 values:

- `scheduled` - Match is scheduled for a future date/time
- `in_progress` - Match is currently being played  
- `completed` - Match has finished
- `cancelled` - Match has been cancelled

**Note**: There is no "delayed" status in the database.

## Client-Side Status Logic

### Delayed Status Detection

The frontend automatically detects when a scheduled match should be displayed as "delayed":

```typescript
import { getMatchStatusBasedOnDate } from '@/services/matchStatusService';

// This will return 'delayed' if scheduled_date has passed, otherwise returns the original status
const displayStatus = getMatchStatusBasedOnDate(match.scheduled_date, match.status);
```

### Status Display Functions

```typescript
import { getStatusDisplayText, getStatusColor } from '@/services/matchStatusService';

const statusText = getStatusDisplayText(displayStatus); // "Delayed", "Scheduled", etc.
const statusColor = getStatusColor(displayStatus); // "bg-orange-500", "bg-blue-500", etc.
```

## Status Colors

- **Scheduled**: Blue (`bg-blue-500`)
- **Delayed**: Orange (`bg-orange-500`) - *Client-side only*
- **In Progress**: Green (`bg-green-500`)
- **Completed**: Gray (`bg-gray-500`)
- **Cancelled**: Red (`bg-red-500`)

## Benefits of This Approach

1. **Real-time Updates**: No database triggers needed - status updates immediately when time passes
2. **Simplified Backend**: Only 4 core statuses to manage in the database
3. **Consistent Display**: Centralized status display logic across all components
4. **Performance**: No need for periodic database updates or complex triggers

## Usage in Components

```typescript
// In any component displaying match status
import { getMatchStatusBasedOnDate, getStatusDisplayText, getStatusColor } from '@/services/matchStatusService';

const MyMatchComponent = ({ match }) => {
  const actualStatus = getMatchStatusBasedOnDate(match.scheduled_date, match.status);
  const statusText = getStatusDisplayText(actualStatus);
  const statusColor = getStatusColor(actualStatus);
  
  return (
    <View className={`px-3 py-1 rounded-full ${statusColor}`}>
      <Text className="text-white">{statusText}</Text>
    </View>
  );
};
```

## Database Schema

```sql
-- Enum with only 4 values
CREATE TYPE match_status AS ENUM ('scheduled', 'in_progress', 'completed', 'cancelled');

-- Column definition
ALTER TABLE matches ADD COLUMN status match_status DEFAULT 'scheduled' NOT NULL;
```
