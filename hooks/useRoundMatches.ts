import { useState, useCallback, useMemo, useRef } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import {
  fetchMatchesByRounds,
  type RoundMatches,
} from '@/services/scheduleService';
import { toast } from '@/toast/toast';

interface UseRoundMatchesProps {
  tournamentId: string;
  tournamentRoundNames?: string[];
  enabled?: boolean;
}

interface UseRoundMatchesReturn {
  roundMatches: RoundMatches[];
  totalMatches: number;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  isEmpty: boolean;
}

export const useRoundMatches = ({
  tournamentId,
  tournamentRoundNames = [],
  enabled = true,
}: UseRoundMatchesProps): UseRoundMatchesReturn => {
  const [roundMatches, setRoundMatches] = useState<RoundMatches[]>([]);
  const [totalMatches, setTotalMatches] = useState(0);
  const [loading, setLoading] = useState(enabled);
  const [error, setError] = useState<string | null>(null);
  const isFetchingRef = useRef(false);

  const roundNamesString = useMemo(() => {
    return JSON.stringify(tournamentRoundNames);
  }, [tournamentRoundNames]);

  const fetchData = useCallback(async () => {
    if (!tournamentId || !enabled || isFetchingRef.current) return;

    isFetchingRef.current = true;
    setLoading(true);
    setError(null);

    try {
      const result = await fetchMatchesByRounds(
        tournamentId,
        tournamentRoundNames
      );

      if (result.error) {
        setError(result.error);
        setRoundMatches([]);
        setTotalMatches(0);
        toast.error('Something went wrong!');
      } else {
        setRoundMatches(result.roundMatches);
        setTotalMatches(result.totalMatches);
        setError(null);
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to fetch matches';
      setError(errorMessage);
      setRoundMatches([]);
      setTotalMatches(0);
      toast.error('Something went wrong!');
    } finally {
      setLoading(false);
      isFetchingRef.current = false;
    }
  }, [tournamentId, roundNamesString, enabled]);

  const isEmpty = useMemo(() => {
    return totalMatches === 0 && !loading;
  }, [totalMatches, loading]);

  useFocusEffect(
    useCallback(() => {
      if (enabled && tournamentId) {
        fetchData();
      }
    }, [fetchData, enabled, tournamentId])
  );

  return {
    roundMatches,
    totalMatches,
    loading,
    error,
    refetch: fetchData,
    isEmpty,
  };
};
