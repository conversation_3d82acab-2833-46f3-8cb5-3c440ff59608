import { useState, useCallback } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import { fetchMatchesForTournament } from '@/services/matchService';
import { toast } from '@/toast/toast';
import { type Match } from '@/types/matches';

interface UseTournamentMatchesProps {
  tournamentId: string;
  autoFetch?: boolean;
}

interface UseTournamentMatchesReturn {
  matches: Match[];
  totalCount: number;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useTournamentMatches = (
  tournamentId: string,
  autoFetch: boolean = true
): UseTournamentMatchesReturn => {
  const [matches, setMatches] = useState<Match[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(autoFetch);
  const [error, setError] = useState<string | null>(null);

  const fetchMatches = useCallback(async () => {
    if (!tournamentId) return;

    setLoading(true);
    setError(null);

    try {
      const { matches: fetchedMatches, error: fetchError } =
        await fetchMatchesForTournament(tournamentId);

      if (fetchError) {
        setError(fetchError);
        setMatches([]);
        setTotalCount(0);
        toast.error(fetchError || 'Something went wrong!');
      } else {
        setMatches(fetchedMatches || []);
        setTotalCount(fetchedMatches?.length || 0);
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to fetch matches';
      setError(errorMessage);
      setMatches([]);
      setTotalCount(0);
      toast.error('Something went wrong!');
    } finally {
      setLoading(false);
    }
  }, [tournamentId]);

  useFocusEffect(
    useCallback(() => {
      if (autoFetch) {
        fetchMatches();
      }
    }, [fetchMatches, autoFetch])
  );

  return {
    matches,
    totalCount,
    loading,
    error,
    refetch: fetchMatches,
  };
};
