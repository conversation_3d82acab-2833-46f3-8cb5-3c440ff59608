import { useFocusEffect } from 'expo-router';
import { useCallback } from 'react';
import { StatusBar as RNStatusBar, Platform } from 'react-native';

export function useStatusBarColor(
  barStyle: 'light-content' | 'dark-content' = 'light-content'
) {
  useFocusEffect(
    useCallback(() => {
      if (Platform.OS === 'android') {
        RNStatusBar.setTranslucent(true);
        RNStatusBar.setBackgroundColor('transparent');
      }
      RNStatusBar.setBarStyle(barStyle);

      return () => {
        if (Platform.OS === 'android') {
          RNStatusBar.setTranslucent(false);
          RNStatusBar.setBackgroundColor('#ffffff');
        }
        RNStatusBar.setBarStyle('dark-content');
      };
    }, [barStyle])
  );
}
