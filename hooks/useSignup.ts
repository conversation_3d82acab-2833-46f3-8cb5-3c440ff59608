import { useState } from 'react';
import { supabase } from '@/lib/supabase';
import { validateEmail, validatePassword } from '@/utils';
import { useAuthUser } from '@/hooks/useAuthUser';
import {
  handleAuthError,
  getAuthErrorMessage,
} from '@/utils/auth-error-handler';

export function useSignup() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setUser } = useAuthUser();

  const signup = async (
    name: string,
    email: string,
    password: string
  ): Promise<boolean> => {
    if (!name.trim()) {
      setError('Name is required.');
      return false;
    }

    const emailError = validateEmail(email);
    const passwordError = validatePassword(password);

    if (emailError || passwordError) {
      setError(emailError || passwordError);
      return false;
    }

    try {
      setLoading(true);
      setError(null);

      const { data, error: signupError } = await supabase.auth.signUp({
        email: email.trim(),
        password,
        options: {
          data: { full_name: name.trim() },
        },
      });

      if (signupError) {
        await handleAuthError(signupError, false);
        const errorMessage = signupError.code
          ? getAuthErrorMessage(signupError.code)
          : signupError.message || 'Something went wrong.';
        setError(errorMessage);
        return false;
      }

      if (data.user) {
        setUser(data.user); // TODO: need to handle this
      }

      return true;
    } catch (err: any) {
      await handleAuthError(err, false);
      setError('An unexpected error occurred.');
      return false;
    } finally {
      setLoading(false);
    }
  };

  return { signup, loading, error, setError };
}
