import { useState, useCallback } from 'react';
import { fetchPlayers } from '@/services/playerService';
import { type Player } from '@/types/player';
import { type Option } from '@/components/k-components/AsyncSelectWithSearch';

interface UseAvailablePlayersProps {
  tournamentId: string;
  excludePlayerIds?: string[];
  limit?: number;
}

interface UseAvailablePlayersReturn {
  availablePlayers: Option[];
  loading: boolean;
  error: string | null;
  loadPlayers: (search?: string) => Promise<void>;
  clearPlayers: () => void;
}

export const useAvailablePlayers = ({
  tournamentId,
  excludePlayerIds = [],
  limit = 50,
}: UseAvailablePlayersProps): UseAvailablePlayersReturn => {
  const [availablePlayers, setAvailablePlayers] = useState<Option[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadPlayers = useCallback(
    async (search = '') => {
      if (!tournamentId) return;

      setLoading(true);
      setError(null);

      try {
        const { players, error: fetchError } = await fetchPlayers({
          tournamentId,
          search,
          limit,
        });

        if (fetchError) {
          setError(fetchError);
          setAvailablePlayers([]);
        } else if (players) {
          // Filter out excluded players and convert to options
          const filteredPlayers = players.filter(
            (player) => !excludePlayerIds.includes(player.id)
          );

          const options: Option[] = filteredPlayers.map((player) => ({
            label: player.name,
            value: player.id,
            ...player,
          }));

          setAvailablePlayers(options);
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch players');
        setAvailablePlayers([]);
      } finally {
        setLoading(false);
      }
    },
    [tournamentId, excludePlayerIds, limit]
  );

  const clearPlayers = useCallback(() => {
    setAvailablePlayers([]);
    setError(null);
  }, []);

  return {
    availablePlayers,
    loading,
    error,
    loadPlayers,
    clearPlayers,
  };
};
