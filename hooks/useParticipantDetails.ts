import { useState, useCallback, useMemo, useRef } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import {
  ParticipantType,
  PARTICIPANT_TYPES,
  TeamParticipant,
  PlayerParticipant,
  PairParticipant,
  ParticipantDetails,
  ParticipantCache,
  UseParticipantDetailsProps,
  UseParticipantDetailsReturn,
} from '../types/participants';
import { fetchTeamById, fetchPairById } from '../services/teamsService';
import { fetchPlayerById } from '../services/playerService';

export type {
  TeamParticipant,
  PlayerParticipant,
  PairParticipant,
  ParticipantDetails,
  UseParticipantDetailsProps,
  UseParticipantDetailsReturn,
} from '../types/participants';

const participantCache: ParticipantCache = {};

const participantServices = {
  [PARTICIPANT_TYPES.TEAM]: {
    fetch: fetchTeamById,
    transform: (data: any): TeamParticipant => ({
      id: data.team.id,
      name: data.team.name,
      type: 'team',
      short_name: data.team.short_name,
      logo_url: data.team.logo_url,
      captain_id: data.team.captain_id,
    }),
    dataKey: 'team',
  },
  [PARTICIPANT_TYPES.PLAYER]: {
    fetch: fetchPlayerById,
    transform: (data: any): PlayerParticipant => ({
      id: data.player.id,
      name: data.player.name,
      type: 'player',
      jersey_number: data.player.jersey_number,
      team_id: data.player.team_id,
    }),
    dataKey: 'player',
  },
  [PARTICIPANT_TYPES.PAIR]: {
    fetch: fetchPairById,
    transform: (data: any): PairParticipant => ({
      id: data.data.id,
      name: data.data.name,
      type: 'pair',
      player_1_name: data.data.player_1_name,
      player_2_name: data.data.player_2_name,
      player_1_id: data.data.player_1_id,
      player_2_id: data.data.player_2_id,
    }),
    dataKey: 'data',
  },
};

const createFallbackParticipant = (
  name: string,
  type: ParticipantType
): ParticipantDetails | null => {
  switch (type) {
    case PARTICIPANT_TYPES.TEAM:
      return { id: 'fallback', name, type: 'team' };

    case PARTICIPANT_TYPES.PLAYER:
      return { id: 'fallback', name, type: 'player' };

    case PARTICIPANT_TYPES.PAIR:
      const [player1, player2] = name.split(/\s*[/&]\s*/);
      return {
        id: 'fallback',
        name,
        type: 'pair',
        player_1_name: player1 || name,
        player_2_name: player2 || name,
      };

    default:
      return null;
  }
};

export const useParticipantDetails = ({
  participantId,
  participantType,
  participantName,
  enableCache = true,
  cacheExpiryMs = 5 * 60 * 1000, // 5 minutes default
}: UseParticipantDetailsProps): UseParticipantDetailsReturn => {
  const [participant, setParticipant] = useState<ParticipantDetails | null>(
    null
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const isFetchingRef = useRef<boolean>(false);

  const fallbackParticipant = useMemo(() => {
    return participantName
      ? createFallbackParticipant(participantName, participantType)
      : null;
  }, [participantName, participantType]);

  // Generate cache key
  const getCacheKey = useCallback((id: string, type: ParticipantType) => {
    return `${type}_${id}`;
  }, []);

  const isCacheValid = useCallback(
    (cacheKey: string) => {
      if (!enableCache || !participantCache[cacheKey]) return false;
      const { timestamp } = participantCache[cacheKey];
      return Date.now() - timestamp < cacheExpiryMs;
    },
    [enableCache, cacheExpiryMs]
  );

  const getFromCache = useCallback(
    (cacheKey: string): ParticipantDetails | null => {
      if (!enableCache || !isCacheValid(cacheKey)) return null;
      return participantCache[cacheKey].data;
    },
    [enableCache, isCacheValid]
  );

  // Set data to cache
  const setToCache = useCallback(
    (cacheKey: string, data: ParticipantDetails) => {
      if (!enableCache) return;
      participantCache[cacheKey] = {
        data,
        timestamp: Date.now(),
      };
    },
    [enableCache]
  );

  const clearCacheEntry = useCallback((cacheKey: string) => {
    delete participantCache[cacheKey];
  }, []);

  const clearCache = useCallback(() => {
    Object.keys(participantCache).forEach((key) => {
      delete participantCache[key];
    });
  }, []);

  const fetchParticipantDetails = useCallback(
    async (forceRefetch = false) => {
      if (!participantId || isFetchingRef.current) {
        if (!participantId) {
          setParticipant(fallbackParticipant);
          setError(null);
          setLoading(false);
        }
        return;
      }

      const cacheKey = getCacheKey(participantId, participantType);

      if (!forceRefetch) {
        const cachedData = getFromCache(cacheKey);
        if (cachedData) {
          setParticipant(cachedData);
          setError(null);
          setLoading(false);
          return;
        }
      }

      isFetchingRef.current = true;
      setLoading(true);
      setError(null);

      try {
        const service = participantServices[participantType];

        if (!service) {
          throw new Error(`Unknown participant type: ${participantType}`);
        }

        const result = await service.fetch(participantId);

        if (result?.success && result[service.dataKey as keyof typeof result]) {
          const transformedParticipant = service.transform(result);

          // Cache the result
          setToCache(cacheKey, transformedParticipant);

          setParticipant(transformedParticipant);
          setError(null);
        } else {
          setError('Something went wrong');
          setParticipant(fallbackParticipant);
        }
      } catch (err: any) {
        setError('Something went wrong');
        setParticipant(fallbackParticipant);
      } finally {
        setLoading(false);
        isFetchingRef.current = false;
      }
    },
    [
      participantId,
      participantType,
      fallbackParticipant,
      getCacheKey,
      getFromCache,
      setToCache,
    ]
  );

  useFocusEffect(
    useCallback(() => {
      fetchParticipantDetails();
      return () => {
        if (participantId) {
          const cacheKey = getCacheKey(participantId, participantType);
          clearCacheEntry(cacheKey);
        }
      };
    }, [
      fetchParticipantDetails,
      participantId,
      participantType,
      getCacheKey,
      clearCacheEntry,
    ])
  );

  return {
    participant,
    loading,
    error,
    refetch: fetchParticipantDetails,
    clearCache,
  };
};
