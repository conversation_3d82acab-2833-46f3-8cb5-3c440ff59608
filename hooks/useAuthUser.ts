import { useEffect, useState, useCallback } from 'react';
import { supabase } from '@/lib/supabase';
import { User } from '@supabase/supabase-js';
import { handleAuthError } from '@/utils/auth-error-handler';

export function useAuthUser() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const refreshUser = useCallback(async (shouldSetLoading = false) => {
    if (shouldSetLoading) setLoading(true);

    try {
      const {
        data: { session },
        error,
      } = await supabase.auth.getSession();

      if (error) {
        await handleAuthError(error);
        setUser(null);
      } else {
        setUser(session?.user ?? null);
      }
    } catch (err: any) {
      await handleAuthError(err);
      setUser(null);
    } finally {
      if (shouldSetLoading) setLoading(false);
    }
  }, []);

  useEffect(() => {
    // Initial session fetch with loading state
    refreshUser(true);

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      if (
        event === 'SIGNED_OUT' ||
        event === 'TOKEN_REFRESHED' ||
        event === 'SIGNED_IN' ||
        event === 'USER_UPDATED'
      ) {
        setUser(session?.user ?? null);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [refreshUser]);

  return { user, loading, setUser, refreshUser };
}
