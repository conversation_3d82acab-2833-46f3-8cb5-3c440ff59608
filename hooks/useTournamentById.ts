import { useState, useCallback } from 'react';
import { useFocusEffect } from 'expo-router';
import { fetchTournamentById } from '@/services/tournamentService';
import { toast } from '@/toast/toast';
import { Tournament } from '@/types/tournament';

interface UseTournamentByIdReturn {
  tournament: Tournament | null;
  loading: boolean;
  refetch: () => void;
}

export function useTournamentById(
  tournamentId: string | undefined
): UseTournamentByIdReturn {
  const [tournament, setTournament] = useState<Tournament | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchTournament = useCallback(async () => {
    if (!tournamentId) {
      setLoading(false);
      return;
    }

    setLoading(true);

    try {
      const res = await fetchTournamentById(tournamentId);

      if (res.success) {
        setTournament(res.data || null);
      } else {
        toast.error(res.error || 'Failed to load tournament');
        setTournament(null);
      }
    } catch (error) {
      toast.error('Something went wrong while loading tournament');
      setTournament(null);
    } finally {
      setLoading(false);
    }
  }, [tournamentId]);

  useFocusEffect(
    useCallback(() => {
      fetchTournament();
    }, [fetchTournament])
  );

  const refetch = useCallback(() => {
    fetchTournament();
  }, [fetchTournament]);

  return {
    tournament,
    loading,
    refetch,
  };
}
