import { useRef, useCallback } from 'react';
import { GestureResponderEvent } from 'react-native';

interface TouchPoint {
  x: number;
  y: number;
  time: number;
}

interface UseScrollAwareTapOptions {
  onTap: () => void;
  scrollThreshold?: number; 
  timeThreshold?: number; 
  disabled?: boolean; 
}

interface UseScrollAwareTapReturn {
  onTouchStart: (event: GestureResponderEvent) => void;
  onTouchEnd: (event: GestureResponderEvent) => void;
  onTouchCancel: (event: GestureResponderEvent) => void;
}


export const useScrollAwareTap = ({
  onTap,
  scrollThreshold = 10,
  timeThreshold = 300,
  disabled = false,
}: UseScrollAwareTapOptions): UseScrollAwareTapReturn => {
  const touchStartRef = useRef<TouchPoint | null>(null);

  const onTouchStart = useCallback((event: GestureResponderEvent) => {
    if (disabled) return;

    const { pageX, pageY } = event.nativeEvent;
    touchStartRef.current = {
      x: pageX,
      y: pageY,
      time: Date.now(),
    };
  }, [disabled]);

  const onTouchEnd = useCallback((event: GestureResponderEvent) => {
    if (disabled || !touchStartRef.current) {
      touchStartRef.current = null;
      return;
    }

    const { pageX, pageY } = event.nativeEvent;
    const { x: startX, y: startY, time: startTime } = touchStartRef.current;
    
    const deltaX = Math.abs(pageX - startX);
    const deltaY = Math.abs(pageY - startY);
    const deltaTime = Date.now() - startTime;
    
    // Check if it's a valid tap (small movement, quick time)
    const isTap = deltaX < scrollThreshold && 
                  deltaY < scrollThreshold && 
                  deltaTime < timeThreshold;
    
    if (isTap) {
      onTap();
    }
    
    touchStartRef.current = null;
  }, [disabled, onTap, scrollThreshold, timeThreshold]);

  const onTouchCancel = useCallback((event: GestureResponderEvent) => {
    // Reset touch tracking when touch is cancelled (e.g., by scroll view)
    touchStartRef.current = null;
  }, []);

  return {
    onTouchStart,
    onTouchEnd,
    onTouchCancel,
  };
};

// Alternative hook for TouchableWithoutFeedback components
export const useScrollAwareTapWithoutFeedback = ({
  onTap,
  scrollThreshold = 15, // Slightly higher threshold for TouchableWithoutFeedback
  timeThreshold = 500, // Longer time threshold
  disabled = false,
}: UseScrollAwareTapOptions) => {
  const touchStartRef = useRef<TouchPoint | null>(null);

  const onPressIn = useCallback((event: GestureResponderEvent) => {
    if (disabled) return;

    const { pageX, pageY } = event.nativeEvent;
    touchStartRef.current = {
      x: pageX,
      y: pageY,
      time: Date.now(),
    };
  }, [disabled]);

  const onPressOut = useCallback((event: GestureResponderEvent) => {
    if (disabled || !touchStartRef.current) {
      touchStartRef.current = null;
      return;
    }

    const { pageX, pageY } = event.nativeEvent;
    const { x: startX, y: startY, time: startTime } = touchStartRef.current;
    
    const deltaX = Math.abs(pageX - startX);
    const deltaY = Math.abs(pageY - startY);
    const deltaTime = Date.now() - startTime;
    
    // More lenient tap detection for TouchableWithoutFeedback
    const isTap = deltaX < scrollThreshold && 
                  deltaY < scrollThreshold && 
                  deltaTime < timeThreshold;
    
    if (isTap) {
      onTap();
    }
    
    touchStartRef.current = null;
  }, [disabled, onTap, scrollThreshold, timeThreshold]);

  return {
    onPressIn,
    onPressOut,
  };
};

export default useScrollAwareTap;
