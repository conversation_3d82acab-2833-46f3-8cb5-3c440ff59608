import { useState, useCallback, useEffect } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import { fetchPlayers } from '@/services/playerService';
import { type Player } from '@/types/player';

interface UseTeamPlayersProps {
  tournamentId: string;
  teamId: string;
  limit?: number;
  autoFetch?: boolean;
}

interface UseTeamPlayersReturn {
  players: Player[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useTeamPlayers = ({
  tournamentId,
  teamId,
  limit = 50,
  autoFetch = true,
}: UseTeamPlayersProps): UseTeamPlayersReturn => {
  const [players, setPlayers] = useState<Player[]>([]);
  const [loading, setLoading] = useState(autoFetch);
  const [error, setError] = useState<string | null>(null);

  const fetchTeamPlayers = useCallback(async () => {
    if (!tournamentId || !teamId) return;

    setLoading(true);
    setError(null);

    try {
      const { players: fetchedPlayers, error: fetchError } = await fetchPlayers(
        {
          tournamentId,
          teamId,
          limit,
        }
      );

      if (fetchError) {
        setError(fetchError);
        setPlayers([]);
      } else {
        setPlayers(fetchedPlayers || []);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch players');
      setPlayers([]);
    } finally {
      setLoading(false);
    }
  }, [tournamentId, teamId, limit]);

  useFocusEffect(
    useCallback(() => {
      if (autoFetch) {
        fetchTeamPlayers();
      }
    }, [fetchTeamPlayers, autoFetch])
  );

  return {
    players,
    loading,
    error,
    refetch: fetchTeamPlayers,
  };
};
