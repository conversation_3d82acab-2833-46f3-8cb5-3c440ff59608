import React from 'react';
import Svg, { Path } from 'react-native-svg';

export const ProfileIcon: React.FC<{ size?: number; color?: string }> = ({
  size = 84,
  color = 'white',
}) => (
  <Svg width={size} height={size} viewBox="0 0 84 84" fill="none">
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M42.0002 41.9999C46.6027 41.9999 50.3335 38.2691 50.3335 33.6666C50.3335 29.0642 46.6027 25.3333 42.0002 25.3333C37.3977 25.3333 33.6668 29.0642 33.6668 33.6666C33.6668 38.2691 37.3977 41.9999 42.0002 41.9999ZM42.0002 48.2499C50.0543 48.2499 56.5835 41.7208 56.5835 33.6666C56.5835 25.6125 50.0543 19.0833 42.0002 19.0833C33.946 19.0833 27.4168 25.6125 27.4168 33.6666C27.4168 41.7208 33.946 48.2499 42.0002 48.2499Z"
      fill={color}
      fillOpacity={0.29}
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M37.7402 83.4516C39.1406 83.5937 40.5618 83.6666 42.0002 83.6666C65.0118 83.6666 83.6668 65.0116 83.6668 41.9999C83.6668 18.988 65.0118 0.333252 42.0002 0.333252C18.9883 0.333252 0.333496 18.988 0.333496 41.9999C0.333496 52.3395 4.09971 61.7999 10.3352 69.0833C16.0606 75.7708 23.8678 80.6233 32.7527 82.6366C34.381 83.0058 36.0456 83.2795 37.7402 83.4516ZM64.8235 69.0828C58.8189 74.1483 51.1068 77.2533 42.6735 77.4103C42.4497 77.4145 42.2252 77.4166 42.0002 77.4166C41.8206 77.4166 41.6418 77.4153 41.4631 77.4124C32.9767 77.2866 25.2136 74.1753 19.1767 69.0828C25.3445 63.8787 33.3047 60.7499 42.0002 60.7499C50.6956 60.7499 58.6556 63.8787 64.8235 69.0828ZM69.2398 64.6362C61.9356 58.3203 52.4139 54.4999 42.0002 54.4999C31.5862 54.4999 22.0645 58.3203 14.7604 64.6362C9.65437 58.4987 6.5835 50.6078 6.5835 41.9999C6.5835 22.4398 22.4401 6.58325 42.0002 6.58325C61.5602 6.58325 77.4168 22.4398 77.4168 41.9999C77.4168 50.6078 74.346 58.4987 69.2398 64.6362Z"
      fill={color}
      fillOpacity={0.29}
    />
  </Svg>
);
