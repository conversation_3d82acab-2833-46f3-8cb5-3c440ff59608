import dayjs from 'dayjs';
import { toast } from '@/toast/toast';
import { Tournament } from '@/types/tournament';

export function getEventStatus(
  startDate: string,
  endDate: string
): 'upcoming' | 'live' | 'completed' {
  const now = dayjs();
  const start = dayjs(startDate);
  const end = dayjs(endDate);

  if (now.isBefore(start, 'day')) return 'upcoming';
  if (
    (now.isSame(start, 'day') || now.isAfter(start, 'day')) &&
    (now.isBefore(end, 'day') || now.isSame(end, 'day'))
  ) {
    return 'live';
  }
  return 'completed';
}


export function isTournamentCompleted(
  tournamentOrEndDate: string | undefined | null,
  showToast: boolean = true
): boolean {
  if (!tournamentOrEndDate) {
    return false;
  }

  const now = dayjs();
  const end = dayjs(tournamentOrEndDate);
  const isCompleted = now.isAfter(end, 'day');

  if (isCompleted && showToast) {
    toast.info('Tournament has ended. Change the tournament end date to continue.');
  }

  return isCompleted;
}

export function transformTournamentFormData(formData: any) {
  const locationData = formData.location?.data || {};

  return {
    name: formData.name,
    sport_type: formData.type,
    venue: formData.venue,
    logo_url: formData.logo || '',

    // Flattened location fields
    place_class: locationData.class || '',
    place_type: locationData.type || '',
    display_address: locationData.display_address || '',
    display_name: locationData.display_name || '',
    display_place: locationData.display_place || '',
    lat: parseFloat(locationData.lat) || null,
    lon: parseFloat(locationData.lon) || null,
    licence: locationData.licence || '',
    osm_id: locationData.osm_id || '',
    osm_type: locationData.osm_type || '',
    place_id: locationData.place_id || '',
    location_description: formData.location?.description || '',

    start_date: formData.tournamentDuration?.startDate
      ? new Date(formData.tournamentDuration.startDate)
      : null,

    end_date: formData.tournamentDuration?.endDate
      ? new Date(formData.tournamentDuration.endDate)
      : null,
  };
}
