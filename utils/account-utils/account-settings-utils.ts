import { ShieldIcon, FileTextIcon, UserIcon } from 'lucide-react-native';
import SCREENS from '@/constants/Screens';

const nonLoggedinSettingsItems = [
  {
    title: 'Privacy Policy',
    icon: ShieldIcon,
    path: SCREENS.PRIVACY_POLICY,
  },
  {
    title: 'Terms & Conditions',
    icon: FileTextIcon,
    path: SCREENS.TERMS_AND_CONDITIONS,
  },
];

const loggedInSettingsItems = [
  {
    title: 'Profile Settings',
    icon: UserIcon,
    path: SCREENS.PROFILE_SETTINGS,
  },
  ...nonLoggedinSettingsItems,
];

export { nonLoggedinSettingsItems, loggedInSettingsItems };
