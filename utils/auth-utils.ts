import { supabase } from '@/lib/supabase';

export async function getCurrentUser() {
  try {
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (!user) {
      return {
        success: false,
        error: userError?.message || 'User not authenticated',
        user: null,
      };
    }

    return {
      success: true,
      user,
    };
  } catch (err: any) {
    return {
      success: false,
      error: err.message || 'Unknown error occurred',
      user: null,
    };
  }
}
