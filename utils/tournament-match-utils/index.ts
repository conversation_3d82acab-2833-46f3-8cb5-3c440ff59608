import { toast } from '@/toast/toast';
import { createOption, type Option } from '../option-utils';
import type { CreateMatchInput } from '@/types/matches';
import type { ParticipantType } from '@/types/participants';
import { isEmpty } from 'lodash';

export const createCustomOption = async (
  label: string,
  createFn: (label: string) => Promise<{ success: boolean; error?: string }>,
  updateOptionsFn: (newOption: Option) => void,
  successMessage: string
): Promise<Option> => {
  const newOption = createOption(label);

  try {
    const result = await createFn(label);

    if (!result.success) {
      toast.error(result.error || `Failed to create ${label}`);
      throw new Error(result.error || `Failed to create ${label}`);
    }

    updateOptionsFn(newOption);

    await new Promise((resolve) => setTimeout(resolve, 10));

    toast.success(successMessage);
    return newOption;
  } catch (error: any) {
    toast.error(error.message || `Failed to create ${label}`);
    throw new Error(error.message || `Failed to create ${label}`);
  }
};

export const handleSelectFieldChange = (
  key: string,
  value: string,
  options: Option[],
  onChange: (key: string, value: Option | null) => void
): void => {
  let selectedOption = options.find((opt) => opt.value === value);

  if (!selectedOption && value) {
    selectedOption = createOption(value);
  }

  onChange(key, selectedOption || null);
};

export const getSelectFieldValue = (
  fieldKey: string,
  form: Record<string, any>
): string => {
  const fieldValue = form[fieldKey];

  switch (fieldKey) {
    case 'scheduled_date':
      return fieldValue || '';
    case 'stage':
    case 'court_field_number':
      return fieldValue?.value || '';
    default:
      return fieldValue;
  }
};

export const getParticipantLogoFallback = (
  participant: any,
  fallbackText?: string
): string => {
  const isTBD = isEmpty(participant?.id) || participant?.id === 'fallback';
  if (isTBD) {
    return '?';
  }

  return fallbackText || '?';
};

export const formatMatchData = (
  form: Record<string, any>,
  tournamentId: string,
  participantType: ParticipantType,
  formatParticipantName: (option: Option | null) => string | null
): CreateMatchInput => ({
  tournament_id: tournamentId,
  participant_type: participantType,
  participant_1_id: form.teamA?.value || null,
  participant_1_name: formatParticipantName(form.teamA),
  participant_2_id: form.teamB?.value || null,
  participant_2_name: formatParticipantName(form.teamB),
  scheduled_date: form.scheduled_date || null,
  stage: form.stage?.label || null,
  court_field_number: form.court_field_number?.label || null,
});
