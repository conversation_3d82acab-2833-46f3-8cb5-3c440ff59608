import { getMatchStatusBasedOnDate } from '@/services/matchStatusService';

export interface MatchAction {
  id: string;
  title: string;
  type: 'primary' | 'secondary' | 'danger' | 'warning';
  icon?: string;
  requiresConfirmation?: boolean;
  confirmationMessage?: string;
}

export interface MatchActionHandlers {
  onStartMatch?: () => void;
  onEditMatch?: () => void;
  onCancelMatch?: () => void;
  onDeleteMatch?: () => void;
  onRescheduleMatch?: () => void;
  onMarkCompleted?: () => void;
  onViewResults?: () => void;
  onAddResults?: () => void;
  onEditResults?: () => void;
  onResumeMatch?: () => void;
  onRestartMatch?: () => void;
  onUncancelMatch?: () => void;
}

/**
 * Get available actions for a match based on its current status
 * @param scheduledDate - The scheduled date of the match
 * @param currentStatus - The current database status of the match
 * @param hasResults - Whether the match has results/scores
 * @returns Array of available actions
 */
export function getMatchActions(
  scheduledDate: string | null,
  currentStatus: string,
  hasResults: boolean = false
): MatchAction[] {
  // Get the actual display status (including client-side delayed logic)
  const actualStatus = getMatchStatusBasedOnDate(scheduledDate, currentStatus);
  
  const actions: MatchAction[] = [];

  switch (actualStatus) {
    case 'scheduled':
      actions.push(
        {
          id: 'start',
          title: 'Start Match',
          type: 'primary',
          icon: 'play',
        },
        {
          id: 'edit',
          title: 'Edit Match',
          type: 'secondary',
          icon: 'edit',
        },
        {
          id: 'reschedule',
          title: 'Reschedule',
          type: 'secondary',
          icon: 'calendar',
        },
        {
          id: 'cancel',
          title: 'Cancel Match',
          type: 'danger',
          icon: 'x-circle',
          requiresConfirmation: true,
          confirmationMessage: 'Are you sure you want to cancel this match? This action cannot be undone.',
        },
        {
          id: 'delete',
          title: 'Delete Match',
          type: 'danger',
          icon: 'trash',
          requiresConfirmation: true,
          confirmationMessage: 'Are you sure you want to delete this match? This action cannot be undone.',
        }
      );
      break;

    case 'delayed':
      actions.push(
        {
          id: 'start',
          title: 'Start Match',
          type: 'primary',
          icon: 'play',
        },
        {
          id: 'reschedule',
          title: 'Reschedule',
          type: 'warning',
          icon: 'calendar',
        },
        {
          id: 'edit',
          title: 'Edit Match',
          type: 'secondary',
          icon: 'edit',
        },
        {
          id: 'cancel',
          title: 'Cancel Match',
          type: 'danger',
          icon: 'x-circle',
          requiresConfirmation: true,
          confirmationMessage: 'Are you sure you want to cancel this delayed match?',
        }
      );
      break;

    case 'in_progress':
      actions.push(
        {
          id: 'add_results',
          title: 'Add Results',
          type: 'primary',
          icon: 'plus-circle',
        },
        {
          id: 'mark_completed',
          title: 'Mark Completed',
          type: 'primary',
          icon: 'check-circle',
        },
        {
          id: 'edit',
          title: 'Edit Match',
          type: 'secondary',
          icon: 'edit',
        },
        {
          id: 'cancel',
          title: 'Cancel Match',
          type: 'danger',
          icon: 'x-circle',
          requiresConfirmation: true,
          confirmationMessage: 'Are you sure you want to cancel this ongoing match?',
        }
      );
      break;

    case 'completed':
      actions.push(
        {
          id: 'view_results',
          title: 'View Results',
          type: 'primary',
          icon: 'eye',
        }
      );
      
      if (hasResults) {
        actions.push({
          id: 'edit_results',
          title: 'Edit Results',
          type: 'secondary',
          icon: 'edit',
        });
      }
      
      actions.push(
        {
          id: 'restart',
          title: 'Restart Match',
          type: 'warning',
          icon: 'rotate-ccw',
          requiresConfirmation: true,
          confirmationMessage: 'Are you sure you want to restart this completed match? This will reset all results.',
        },
        {
          id: 'edit',
          title: 'Edit Match',
          type: 'secondary',
          icon: 'edit',
        }
      );
      break;

    case 'cancelled':
      actions.push(
        {
          id: 'uncancel',
          title: 'Restore Match',
          type: 'primary',
          icon: 'rotate-ccw',
        },
        {
          id: 'edit',
          title: 'Edit Match',
          type: 'secondary',
          icon: 'edit',
        },
        {
          id: 'delete',
          title: 'Delete Match',
          type: 'danger',
          icon: 'trash',
          requiresConfirmation: true,
          confirmationMessage: 'Are you sure you want to permanently delete this cancelled match?',
        }
      );
      break;

    default:
      // Fallback for unknown status
      actions.push({
        id: 'edit',
        title: 'Edit Match',
        type: 'secondary',
        icon: 'edit',
      });
      break;
  }

  return actions;
}

/**
 * Get the button style class based on action type
 */
export function getActionButtonStyle(type: MatchAction['type']): string {
  switch (type) {
    case 'primary':
      return 'bg-blue-600 hover:bg-blue-700';
    case 'secondary':
      return 'bg-gray-600 hover:bg-gray-700';
    case 'danger':
      return 'bg-red-600 hover:bg-red-700';
    case 'warning':
      return 'bg-orange-600 hover:bg-orange-700';
    default:
      return 'bg-gray-600 hover:bg-gray-700';
  }
}
