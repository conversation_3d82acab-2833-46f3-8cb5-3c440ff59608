export interface Option {
  label: string;
  value: string;
  [key: string]: any;
}

export const createOption = (label: string): Option => ({
  label,
  value: label,
});

export const createOptions = (labels: string[]): Option[] =>
  labels.map(createOption);

export const findOptionByValue = (
  options: Option[],
  value: string
): Option | undefined => options.find((opt) => opt.value === value);

export const findOptionByLabel = (
  options: Option[],
  label: string
): Option | undefined =>
  options.find((opt) => opt.label.toLowerCase() === label.toLowerCase());

export const optionExists = (options: Option[], label: string): boolean =>
  !!findOptionByLabel(options, label);

export const mergeOptions = (
  customOptions: Option[],
  predefinedOptions: Option[]
): Option[] => [...customOptions, ...predefinedOptions];

export const createAndMergeOptions = (
  customLabels: string[],
  predefinedOptions: Option[]
): Option[] => {
  const customOptions = createOptions(customLabels);
  return mergeOptions(customOptions, predefinedOptions);
};
