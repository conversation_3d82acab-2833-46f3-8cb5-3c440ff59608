# Google Sign-In Configuration
# Choose ONE of the two approaches below:

# APPROACH 1: With Firebase (Recommended)
# If using Firebase, place google-services.json and GoogleService-Info.plist in your project root
# and uncomment the Firebase config in app.json

# APPROACH 2: Without Firebase
# Get these from Google Cloud Console
EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID=394214528477-uv1ootqiqo0jfmpoaq2bs0ka4m6t2rjh.apps.googleusercontent.com
EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID=394214528477-72jo6lr9t81vm9vdk3n1jr1hvm1n0c6r.apps.googleusercontent.com
# For iOS (without Firebase), you also need:
# EXPO_PUBLIC_GOOGLE_IOS_URL_SCHEME=com.googleusercontent.apps.your_ios_client_id
